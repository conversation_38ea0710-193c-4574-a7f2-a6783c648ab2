/*******************************************************************************
 * Size: 13 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_13
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_13 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_13

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x1f, 0x41, 0xf3, 0xf, 0x30, 0xf2, 0xf, 0x20,
    0xf1, 0xf, 0x10, 0x20, 0xa, 0x21, 0xe4,

    /* U+0022 "\"" */
    0x26, 0x7, 0x14, 0xe0, 0xf2, 0x4d, 0xf, 0x23,
    0xd0, 0xf2,

    /* U+0023 "#" */
    0x0, 0xe, 0x50, 0xb8, 0x0, 0x0, 0xf2, 0xe,
    0x50, 0x0, 0x4f, 0x1, 0xf2, 0x0, 0xff, 0xff,
    0xff, 0xf6, 0x3, 0xc9, 0x3a, 0xb3, 0x10, 0x1f,
    0x30, 0xd6, 0x0, 0xdf, 0xff, 0xff, 0xf8, 0x2,
    0xab, 0x38, 0xe3, 0x10, 0xb, 0x70, 0x9a, 0x0,
    0x0, 0xe4, 0xc, 0x70, 0x0,

    /* U+0024 "$" */
    0x0, 0x5, 0x40, 0x0, 0x0, 0xb, 0x80, 0x0,
    0x1, 0x9e, 0xd7, 0x0, 0xd, 0xdd, 0xdf, 0x90,
    0x4f, 0x1b, 0x76, 0xa0, 0x4f, 0x3b, 0x70, 0x0,
    0xb, 0xfe, 0x90, 0x0, 0x0, 0x5e, 0xfd, 0x20,
    0x0, 0xb, 0x9c, 0xe0, 0x59, 0xb, 0x74, 0xf1,
    0x4f, 0x7c, 0x9a, 0xe0, 0x6, 0xff, 0xfe, 0x30,
    0x0, 0xc, 0x90, 0x0, 0x0, 0xb, 0x80, 0x0,

    /* U+0025 "%" */
    0x1b, 0xfa, 0x0, 0xe, 0x70, 0x8, 0xc2, 0xe6,
    0x8, 0xd0, 0x0, 0xb7, 0x9, 0x91, 0xf5, 0x0,
    0x9, 0xa0, 0xd7, 0xab, 0x0, 0x0, 0x2d, 0xfc,
    0x4f, 0x30, 0x0, 0x0, 0x1, 0xc, 0x90, 0x0,
    0x0, 0x0, 0x5, 0xf1, 0x6d, 0xd5, 0x0, 0x0,
    0xe7, 0x1f, 0x56, 0xf0, 0x0, 0x7e, 0x2, 0xf2,
    0x3f, 0x10, 0x1f, 0x50, 0x7, 0xee, 0x50,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xfc,
    0x20, 0x0, 0x1, 0xf9, 0x4d, 0xb0, 0x0, 0x3,
    0xf3, 0xa, 0xc0, 0x0, 0x0, 0xdb, 0x9f, 0x40,
    0x0, 0x0, 0xaf, 0xe2, 0x9, 0x60, 0xc, 0xd6,
    0xf8, 0xf, 0x50, 0x5f, 0x20, 0x5f, 0xaf, 0x10,
    0x6f, 0x0, 0x7, 0xfa, 0x0, 0x2f, 0x92, 0x3b,
    0xff, 0x30, 0x4, 0xcf, 0xe9, 0x19, 0xf2,

    /* U+0027 "'" */
    0x26, 0x4e, 0x4d, 0x3d,

    /* U+0028 "(" */
    0x0, 0x3, 0x10, 0x6, 0xd0, 0x1, 0xf4, 0x0,
    0x8d, 0x0, 0xd, 0x80, 0x0, 0xf5, 0x0, 0x1f,
    0x40, 0x1, 0xf4, 0x0, 0xf, 0x50, 0x0, 0xc9,
    0x0, 0x7, 0xe0, 0x0, 0xe, 0x60, 0x0, 0x4e,
    0x20,

    /* U+0029 ")" */
    0x21, 0x0, 0x4e, 0x10, 0xa, 0xb0, 0x3, 0xf2,
    0x0, 0xe7, 0x0, 0xba, 0x0, 0xab, 0x0, 0xab,
    0x0, 0xba, 0x0, 0xf6, 0x4, 0xf1, 0xc, 0x90,
    0x6d, 0x0,

    /* U+002A "*" */
    0x0, 0xa5, 0x0, 0x5d, 0xb9, 0xe1, 0x7, 0xff,
    0x30, 0x5d, 0xba, 0xe1, 0x0, 0xa5, 0x10, 0x0,
    0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x4, 0x20, 0x0, 0x0, 0xd6, 0x0, 0x0,
    0xd, 0x60, 0x5, 0xff, 0xff, 0xfe, 0x13, 0x3d,
    0x83, 0x20, 0x0, 0xd6, 0x0, 0x0, 0xd, 0x60,
    0x0,

    /* U+002C "," */
    0xa, 0x40, 0xe9, 0x6, 0x73, 0xa0, 0x0, 0x0,

    /* U+002D "-" */
    0x3f, 0xff, 0xf8, 0x2, 0x22, 0x21,

    /* U+002E "." */
    0x1b, 0x21, 0xe4,

    /* U+002F "/" */
    0x0, 0x6, 0xe0, 0x0, 0xc, 0x80, 0x0, 0x2f,
    0x20, 0x0, 0x7d, 0x0, 0x0, 0xd7, 0x0, 0x3,
    0xf1, 0x0, 0x9, 0xb0, 0x0, 0xe, 0x60, 0x0,
    0x4f, 0x10, 0x0, 0xaa, 0x0, 0x0,

    /* U+0030 "0" */
    0x1, 0xaf, 0xe6, 0x0, 0xae, 0x69, 0xf3, 0x1f,
    0x60, 0xe, 0xa4, 0xf2, 0x0, 0xad, 0x6f, 0x10,
    0x9, 0xe6, 0xf1, 0x0, 0x9e, 0x4f, 0x20, 0xa,
    0xd1, 0xf6, 0x0, 0xea, 0xa, 0xe6, 0x9f, 0x30,
    0x1a, 0xfe, 0x60,

    /* U+0031 "1" */
    0x1, 0xaf, 0x26, 0xfe, 0xf2, 0xb6, 0x4f, 0x20,
    0x4, 0xf2, 0x0, 0x4f, 0x20, 0x4, 0xf2, 0x0,
    0x4f, 0x20, 0x4, 0xf2, 0x0, 0x4f, 0x20, 0x4,
    0xf2,

    /* U+0032 "2" */
    0x2, 0xbf, 0xe6, 0x0, 0xec, 0x59, 0xf4, 0x1c,
    0x10, 0xf, 0x80, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0x7f, 0x20, 0x0, 0x4f, 0x80, 0x0, 0x2f, 0xb0,
    0x0, 0x1e, 0xc0, 0x0, 0xc, 0xf5, 0x44, 0x35,
    0xff, 0xff, 0xfe,

    /* U+0033 "3" */
    0x3, 0xcf, 0xe7, 0x1, 0xeb, 0x59, 0xf4, 0x18,
    0x0, 0xf, 0x70, 0x0, 0x7, 0xf3, 0x0, 0x4f,
    0xf9, 0x0, 0x0, 0x38, 0xf6, 0x0, 0x0, 0xb,
    0xc2, 0xa0, 0x0, 0xbc, 0x1f, 0xb5, 0x8f, 0x70,
    0x3c, 0xfe, 0x70,

    /* U+0034 "4" */
    0x0, 0x4, 0xf3, 0x0, 0x0, 0xc, 0xb0, 0x0,
    0x0, 0x4f, 0x30, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x3, 0xf4, 0x4d, 0x0, 0xb, 0xc0, 0x5f, 0x0,
    0x2f, 0x50, 0x5f, 0x0, 0x8f, 0xff, 0xff, 0xf6,
    0x13, 0x33, 0x7f, 0x31, 0x0, 0x0, 0x5f, 0x0,

    /* U+0035 "5" */
    0x5, 0xff, 0xff, 0x60, 0x7e, 0x33, 0x31, 0x8,
    0xc0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0xc, 0xef,
    0xf8, 0x0, 0xca, 0x47, 0xf7, 0x0, 0x0, 0xc,
    0xc0, 0x31, 0x0, 0xcc, 0xe, 0xb4, 0x8f, 0x60,
    0x3c, 0xfe, 0x70,

    /* U+0036 "6" */
    0x0, 0x4, 0xf4, 0x0, 0x0, 0xd, 0xb0, 0x0,
    0x0, 0x7f, 0x30, 0x0, 0x1, 0xf9, 0x0, 0x0,
    0x9, 0xff, 0xfa, 0x10, 0x1f, 0xd4, 0x6f, 0xa0,
    0x4f, 0x40, 0x9, 0xf0, 0x3f, 0x40, 0x9, 0xf0,
    0xd, 0xd5, 0x7f, 0x90, 0x1, 0xbf, 0xe8, 0x0,

    /* U+0037 "7" */
    0x4f, 0xff, 0xff, 0xc0, 0x33, 0x33, 0xf8, 0x0,
    0x0, 0x4f, 0x20, 0x0, 0xa, 0xc0, 0x0, 0x1,
    0xf6, 0x0, 0x0, 0x7f, 0x10, 0x0, 0xd, 0xa0,
    0x0, 0x3, 0xf4, 0x0, 0x0, 0x9e, 0x0, 0x0,
    0xf, 0x80, 0x0,

    /* U+0038 "8" */
    0x2, 0xbf, 0xe7, 0x0, 0xcd, 0x46, 0xf5, 0xf,
    0x60, 0xd, 0x80, 0xdb, 0x14, 0xf5, 0x2, 0xff,
    0xfb, 0x0, 0xdc, 0x46, 0xf6, 0x5f, 0x20, 0x9,
    0xd5, 0xf2, 0x0, 0x9e, 0x1f, 0xc5, 0x7f, 0x80,
    0x3c, 0xfe, 0x90,

    /* U+0039 "9" */
    0x2, 0xcf, 0xe7, 0x1, 0xec, 0x58, 0xf6, 0x6f,
    0x20, 0xb, 0xc6, 0xf1, 0x0, 0xad, 0x2f, 0x91,
    0x4f, 0xa0, 0x6f, 0xff, 0xf2, 0x0, 0x13, 0xe9,
    0x0, 0x0, 0x8e, 0x10, 0x0, 0x2f, 0x60, 0x0,
    0xb, 0xd0, 0x0,

    /* U+003A ":" */
    0xe, 0x60, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa3, 0xd, 0x60,

    /* U+003B ";" */
    0xc, 0x70, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x95, 0xd, 0xb0, 0x58, 0x1b, 0x10, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x6, 0xb0, 0x2, 0x8e, 0xe7, 0x2b,
    0xfc, 0x50, 0x5, 0xfd, 0x50, 0x0, 0x2, 0x9f,
    0xe7, 0x10, 0x0, 0x6, 0xde, 0x0, 0x0, 0x0,
    0x30,

    /* U+003D "=" */
    0x5f, 0xff, 0xff, 0xe1, 0x33, 0x33, 0x32, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xfe, 0x13, 0x33,
    0x33, 0x20,

    /* U+003E ">" */
    0x59, 0x30, 0x0, 0x2, 0xbf, 0xc5, 0x0, 0x0,
    0x28, 0xee, 0x70, 0x0, 0x18, 0xfd, 0x3, 0xaf,
    0xd6, 0x5, 0xfa, 0x30, 0x0, 0x21, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x1, 0xae, 0xf9, 0x0, 0xcd, 0x57, 0xf8, 0x6,
    0x20, 0xd, 0xa0, 0x0, 0x5, 0xf4, 0x0, 0x5,
    0xf5, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40, 0x0,
    0x0, 0xd6, 0x0,

    /* U+0040 "@" */
    0x0, 0x2, 0x9d, 0xed, 0xa3, 0x0, 0x0, 0x6,
    0xf9, 0x42, 0x38, 0xf8, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x2, 0xe6, 0x0, 0xd6, 0x5, 0xef, 0x8d,
    0x35, 0xd0, 0x2f, 0x2, 0xf8, 0x4b, 0xf3, 0xf,
    0x24, 0xd0, 0x6e, 0x0, 0x1f, 0x30, 0xe4, 0x5c,
    0x7, 0xd0, 0x0, 0xf3, 0xe, 0x33, 0xe0, 0x3f,
    0x40, 0x7f, 0x53, 0xf0, 0xe, 0x40, 0x7f, 0xfb,
    0x7f, 0xf6, 0x0, 0x6e, 0x10, 0x1, 0x0, 0x10,
    0x0, 0x0, 0x9e, 0x61, 0x1, 0x74, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x6, 0xf2, 0x0, 0x0, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x2f, 0x9e, 0x0, 0x0, 0x8, 0xb1,
    0xf4, 0x0, 0x0, 0xe6, 0xb, 0xa0, 0x0, 0x4f,
    0x10, 0x5f, 0x10, 0xa, 0xff, 0xff, 0xf7, 0x0,
    0xf7, 0x33, 0x3a, 0xd0, 0x5f, 0x0, 0x0, 0x4f,
    0x3b, 0xa0, 0x0, 0x0, 0xe9,

    /* U+0042 "B" */
    0xef, 0xff, 0xd5, 0x0, 0xe9, 0x34, 0xaf, 0x40,
    0xe7, 0x0, 0xf, 0x80, 0xe7, 0x0, 0x6f, 0x50,
    0xef, 0xff, 0xfc, 0x0, 0xe8, 0x23, 0x6e, 0xa0,
    0xe7, 0x0, 0x7, 0xf0, 0xe7, 0x0, 0x7, 0xf0,
    0xe9, 0x34, 0x6f, 0xb0, 0xef, 0xff, 0xe9, 0x10,

    /* U+0043 "C" */
    0x0, 0x2a, 0xee, 0xa2, 0x0, 0x3f, 0xc5, 0x5d,
    0xe1, 0xd, 0xc0, 0x0, 0x18, 0x3, 0xf5, 0x0,
    0x0, 0x0, 0x5f, 0x20, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x0, 0x3f, 0x50, 0x0, 0x0, 0x0,
    0xdc, 0x0, 0x1, 0x80, 0x3, 0xfc, 0x55, 0xde,
    0x10, 0x2, 0xbe, 0xfb, 0x20,

    /* U+0044 "D" */
    0xef, 0xfe, 0xb4, 0x0, 0xe9, 0x35, 0xaf, 0x70,
    0xe7, 0x0, 0x8, 0xf3, 0xe7, 0x0, 0x0, 0xe9,
    0xe7, 0x0, 0x0, 0xcb, 0xe7, 0x0, 0x0, 0xcb,
    0xe7, 0x0, 0x0, 0xe9, 0xe7, 0x0, 0x8, 0xf3,
    0xe9, 0x35, 0xaf, 0x70, 0xef, 0xfe, 0xb4, 0x0,

    /* U+0045 "E" */
    0xef, 0xff, 0xff, 0xe, 0x94, 0x44, 0x40, 0xe7,
    0x0, 0x0, 0xe, 0x80, 0x0, 0x0, 0xef, 0xff,
    0xf9, 0xe, 0x93, 0x33, 0x10, 0xe7, 0x0, 0x0,
    0xe, 0x70, 0x0, 0x0, 0xe9, 0x44, 0x44, 0xe,
    0xff, 0xff, 0xf3,

    /* U+0046 "F" */
    0xef, 0xff, 0xff, 0xe, 0x94, 0x44, 0x40, 0xe7,
    0x0, 0x0, 0xe, 0x70, 0x0, 0x0, 0xe8, 0x0,
    0x0, 0xe, 0xff, 0xff, 0x90, 0xe9, 0x33, 0x31,
    0xe, 0x70, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xe,
    0x70, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x2a, 0xef, 0xc4, 0x0, 0x3f, 0xc6, 0x5b,
    0xf4, 0xd, 0xc0, 0x0, 0x7, 0x13, 0xf5, 0x0,
    0x0, 0x0, 0x5f, 0x10, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0xbf, 0xf9, 0x2f, 0x50, 0x2, 0x3d, 0x90,
    0xcd, 0x0, 0x0, 0xc9, 0x2, 0xed, 0x65, 0x8f,
    0x80, 0x2, 0xae, 0xfd, 0x70,

    /* U+0048 "H" */
    0xe7, 0x0, 0x0, 0xab, 0xe7, 0x0, 0x0, 0xab,
    0xe7, 0x0, 0x0, 0xab, 0xe8, 0x0, 0x0, 0xab,
    0xef, 0xff, 0xff, 0xfb, 0xe9, 0x33, 0x33, 0xbb,
    0xe7, 0x0, 0x0, 0xab, 0xe7, 0x0, 0x0, 0xab,
    0xe7, 0x0, 0x0, 0xab, 0xe7, 0x0, 0x0, 0xab,

    /* U+0049 "I" */
    0xe7, 0xe7, 0xe7, 0xe7, 0xe7, 0xe7, 0xe7, 0xe7,
    0xe7, 0xe7,

    /* U+004A "J" */
    0x0, 0x2, 0xf3, 0x0, 0x2, 0xf3, 0x0, 0x2,
    0xf3, 0x0, 0x2, 0xf3, 0x0, 0x2, 0xf3, 0x0,
    0x2, 0xf3, 0x0, 0x2, 0xf3, 0x21, 0x4, 0xf1,
    0xdb, 0x5c, 0xd0, 0x3c, 0xfc, 0x20,

    /* U+004B "K" */
    0xe7, 0x0, 0x8, 0xf4, 0xe, 0x70, 0x6, 0xf5,
    0x0, 0xe7, 0x4, 0xf7, 0x0, 0xe, 0x72, 0xf8,
    0x0, 0x0, 0xe9, 0xef, 0x20, 0x0, 0xe, 0xfb,
    0xec, 0x0, 0x0, 0xec, 0x4, 0xf8, 0x0, 0xe,
    0x70, 0x8, 0xf4, 0x0, 0xe7, 0x0, 0xc, 0xe1,
    0xe, 0x70, 0x0, 0x2f, 0xa0,

    /* U+004C "L" */
    0xe7, 0x0, 0x0, 0xe, 0x70, 0x0, 0x0, 0xe7,
    0x0, 0x0, 0xe, 0x70, 0x0, 0x0, 0xe7, 0x0,
    0x0, 0xe, 0x70, 0x0, 0x0, 0xe7, 0x0, 0x0,
    0xe, 0x70, 0x0, 0x0, 0xe9, 0x44, 0x44, 0xe,
    0xff, 0xff, 0xf1,

    /* U+004D "M" */
    0xe9, 0x0, 0x0, 0x3, 0xf3, 0xef, 0x20, 0x0,
    0xc, 0xf3, 0xef, 0xc0, 0x0, 0x6f, 0xf3, 0xe9,
    0xf5, 0x1, 0xe8, 0xf3, 0xe6, 0x8e, 0x9, 0xc2,
    0xf3, 0xe6, 0xd, 0xbf, 0x32, 0xf3, 0xe6, 0x4,
    0xf9, 0x2, 0xf3, 0xe6, 0x0, 0x51, 0x2, 0xf3,
    0xe6, 0x0, 0x0, 0x2, 0xf3, 0xe6, 0x0, 0x0,
    0x2, 0xf3,

    /* U+004E "N" */
    0xe9, 0x0, 0x0, 0xba, 0xef, 0x40, 0x0, 0xba,
    0xee, 0xe0, 0x0, 0xba, 0xe7, 0xea, 0x0, 0xba,
    0xe6, 0x4f, 0x40, 0xba, 0xe6, 0x9, 0xe1, 0xba,
    0xe6, 0x0, 0xda, 0xba, 0xe6, 0x0, 0x3f, 0xea,
    0xe6, 0x0, 0x8, 0xfa, 0xe6, 0x0, 0x0, 0xda,

    /* U+004F "O" */
    0x0, 0x3b, 0xee, 0xb3, 0x0, 0x4, 0xfc, 0x55,
    0xcf, 0x30, 0xd, 0xc0, 0x0, 0xc, 0xd0, 0x3f,
    0x50, 0x0, 0x5, 0xf2, 0x5f, 0x20, 0x0, 0x2,
    0xf5, 0x5f, 0x20, 0x0, 0x2, 0xf5, 0x3f, 0x50,
    0x0, 0x5, 0xf2, 0xd, 0xc0, 0x0, 0xc, 0xd0,
    0x4, 0xfb, 0x55, 0xcf, 0x30, 0x0, 0x3b, 0xff,
    0xb3, 0x0,

    /* U+0050 "P" */
    0xef, 0xff, 0xc3, 0xe, 0x93, 0x4b, 0xf2, 0xe7,
    0x0, 0xf, 0x8e, 0x70, 0x0, 0xf8, 0xe7, 0x1,
    0x9f, 0x4e, 0xff, 0xfe, 0x60, 0xe9, 0x32, 0x0,
    0xe, 0x70, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xe,
    0x70, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x3b, 0xee, 0xb3, 0x0, 0x0, 0x4f, 0xc5,
    0x5c, 0xf3, 0x0, 0xd, 0xc0, 0x0, 0xc, 0xd0,
    0x3, 0xf5, 0x0, 0x0, 0x5f, 0x20, 0x5f, 0x20,
    0x0, 0x2, 0xf5, 0x5, 0xf2, 0x0, 0x0, 0x2f,
    0x50, 0x3f, 0x50, 0x0, 0x5, 0xf2, 0x0, 0xdc,
    0x0, 0x0, 0xcd, 0x0, 0x4, 0xfb, 0x55, 0xcf,
    0x30, 0x0, 0x3, 0xbf, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90,

    /* U+0052 "R" */
    0xef, 0xff, 0xc3, 0x0, 0xe9, 0x34, 0xbf, 0x20,
    0xe7, 0x0, 0xf, 0x80, 0xe7, 0x0, 0xf, 0x80,
    0xe7, 0x1, 0x9f, 0x40, 0xef, 0xff, 0xf6, 0x0,
    0xe9, 0x39, 0xf1, 0x0, 0xe7, 0x0, 0xdc, 0x0,
    0xe7, 0x0, 0x3f, 0x70, 0xe7, 0x0, 0x8, 0xf3,

    /* U+0053 "S" */
    0x3, 0xcf, 0xea, 0x10, 0x2f, 0xb5, 0x6e, 0xc0,
    0x6f, 0x10, 0x3, 0x60, 0x4f, 0x80, 0x0, 0x0,
    0x8, 0xff, 0xa4, 0x0, 0x0, 0x16, 0xcf, 0x90,
    0x0, 0x0, 0x9, 0xf1, 0x7a, 0x0, 0x4, 0xf2,
    0x5f, 0xa5, 0x5d, 0xd0, 0x4, 0xcf, 0xfa, 0x10,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xf6, 0x34, 0x4f, 0x94, 0x41,
    0x0, 0xf, 0x70, 0x0, 0x0, 0xf, 0x70, 0x0,
    0x0, 0xf, 0x70, 0x0, 0x0, 0xf, 0x70, 0x0,
    0x0, 0xf, 0x70, 0x0, 0x0, 0xf, 0x70, 0x0,
    0x0, 0xf, 0x70, 0x0, 0x0, 0xf, 0x70, 0x0,

    /* U+0055 "U" */
    0xf, 0x60, 0x0, 0xc, 0xa0, 0xf6, 0x0, 0x0,
    0xca, 0xf, 0x60, 0x0, 0xc, 0xa0, 0xf6, 0x0,
    0x0, 0xca, 0xf, 0x60, 0x0, 0xc, 0xa0, 0xf6,
    0x0, 0x0, 0xc9, 0xf, 0x70, 0x0, 0xd, 0x90,
    0xcc, 0x0, 0x3, 0xf5, 0x4, 0xfb, 0x56, 0xed,
    0x0, 0x4, 0xcf, 0xea, 0x10,

    /* U+0056 "V" */
    0xbd, 0x0, 0x0, 0xe, 0x85, 0xf3, 0x0, 0x4,
    0xf2, 0xe, 0x80, 0x0, 0x9c, 0x0, 0x9e, 0x0,
    0xe, 0x60, 0x3, 0xf4, 0x4, 0xf1, 0x0, 0xd,
    0xa0, 0x9b, 0x0, 0x0, 0x7f, 0x1e, 0x50, 0x0,
    0x1, 0xfa, 0xf0, 0x0, 0x0, 0xa, 0xf9, 0x0,
    0x0, 0x0, 0x4f, 0x30, 0x0,

    /* U+0057 "W" */
    0xbb, 0x0, 0x4, 0xf2, 0x0, 0xd, 0x76, 0xf0,
    0x0, 0x9f, 0x70, 0x2, 0xf3, 0x2f, 0x40, 0xd,
    0xfb, 0x0, 0x6e, 0x0, 0xd9, 0x1, 0xf8, 0xf0,
    0xa, 0x90, 0x8, 0xe0, 0x6d, 0x1f, 0x50, 0xe5,
    0x0, 0x3f, 0x2a, 0x80, 0xb9, 0x3f, 0x0, 0x0,
    0xe7, 0xe4, 0x7, 0xe7, 0xb0, 0x0, 0x9, 0xef,
    0x0, 0x2f, 0xe7, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0xdf, 0x20, 0x0, 0x0, 0xf6, 0x0, 0x8, 0xd0,
    0x0,

    /* U+0058 "X" */
    0x6f, 0x40, 0x0, 0x6f, 0x30, 0xbe, 0x0, 0x1f,
    0x80, 0x1, 0xf9, 0xb, 0xd0, 0x0, 0x6, 0xfa,
    0xf3, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x0, 0x0,
    0xcf, 0x90, 0x0, 0x0, 0x7f, 0x8f, 0x40, 0x0,
    0x2f, 0x60, 0xbe, 0x10, 0xd, 0xb0, 0x1, 0xfa,
    0x8, 0xf1, 0x0, 0x6, 0xf5,

    /* U+0059 "Y" */
    0x9f, 0x10, 0x0, 0x9e, 0x1, 0xfa, 0x0, 0x2f,
    0x50, 0x7, 0xf3, 0xb, 0xc0, 0x0, 0xd, 0xd5,
    0xf3, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x7, 0xe0, 0x0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x0, 0x7e, 0x0, 0x0,

    /* U+005A "Z" */
    0x5f, 0xff, 0xff, 0xf1, 0x14, 0x44, 0x5f, 0x90,
    0x0, 0x0, 0x9e, 0x10, 0x0, 0x3, 0xf6, 0x0,
    0x0, 0xc, 0xc0, 0x0, 0x0, 0x6f, 0x30, 0x0,
    0x1, 0xf9, 0x0, 0x0, 0xa, 0xe0, 0x0, 0x0,
    0x4f, 0x94, 0x44, 0x41, 0xbf, 0xff, 0xff, 0xf4,

    /* U+005B "[" */
    0x0, 0x0, 0xef, 0xf6, 0xe8, 0x31, 0xe6, 0x0,
    0xe6, 0x0, 0xe6, 0x0, 0xe6, 0x0, 0xe6, 0x0,
    0xe6, 0x0, 0xe6, 0x0, 0xe6, 0x0, 0xe7, 0x0,
    0xef, 0xf6, 0x33, 0x31,

    /* U+005C "\\" */
    0xaa, 0x0, 0x0, 0x4f, 0x10, 0x0, 0xe, 0x60,
    0x0, 0x9, 0xb0, 0x0, 0x3, 0xf1, 0x0, 0x0,
    0xd7, 0x0, 0x0, 0x7d, 0x0, 0x0, 0x2f, 0x20,
    0x0, 0xc, 0x80, 0x0, 0x6, 0xe0,

    /* U+005D "]" */
    0x0, 0x0, 0xcf, 0xf8, 0x23, 0xd8, 0x0, 0xc8,
    0x0, 0xc8, 0x0, 0xc8, 0x0, 0xc8, 0x0, 0xc8,
    0x0, 0xc8, 0x0, 0xc8, 0x0, 0xc8, 0x0, 0xc8,
    0xcf, 0xf8, 0x23, 0x31,

    /* U+005E "^" */
    0x0, 0xaf, 0x10, 0x1, 0xfe, 0x70, 0x8, 0xc6,
    0xe0, 0xe, 0x50, 0xe5, 0x6e, 0x0, 0x8c,

    /* U+005F "_" */
    0xff, 0xff, 0xfa, 0x22, 0x22, 0x21,

    /* U+0060 "`" */
    0xd, 0x70, 0x2, 0xe1,

    /* U+0061 "a" */
    0x3, 0xcf, 0xd4, 0x0, 0xd9, 0x39, 0xf1, 0x1,
    0x0, 0x1f, 0x40, 0x5c, 0xdc, 0xf5, 0x3f, 0x61,
    0x3f, 0x54, 0xf3, 0x6, 0xf5, 0x8, 0xee, 0x9e,
    0x50,

    /* U+0062 "b" */
    0xf, 0x40, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0xf, 0x7c, 0xfc, 0x20,
    0xf, 0xf7, 0x5c, 0xd0, 0xf, 0x70, 0x2, 0xf4,
    0xf, 0x50, 0x0, 0xf6, 0xf, 0x70, 0x2, 0xf4,
    0xf, 0xe6, 0x4c, 0xd0, 0xf, 0x5c, 0xfc, 0x20,

    /* U+0063 "c" */
    0x1, 0xaf, 0xe7, 0x0, 0xdd, 0x46, 0xd1, 0x4f,
    0x20, 0x0, 0x6, 0xf0, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x0, 0xdc, 0x46, 0xd1, 0x1, 0xbf, 0xe7,
    0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x4, 0xf0,
    0x0, 0x0, 0x4, 0xf0, 0x2, 0xcf, 0xc7, 0xf0,
    0xe, 0xc5, 0x7f, 0xf0, 0x4f, 0x20, 0x8, 0xf0,
    0x6f, 0x0, 0x5, 0xf0, 0x4f, 0x10, 0x7, 0xf0,
    0xe, 0xa2, 0x4e, 0xf0, 0x2, 0xcf, 0xd6, 0xf0,

    /* U+0065 "e" */
    0x2, 0xbf, 0xe6, 0x0, 0xdc, 0x47, 0xf4, 0x4f,
    0x20, 0xc, 0xa6, 0xfe, 0xee, 0xfb, 0x4f, 0x31,
    0x11, 0x10, 0xdb, 0x44, 0xc3, 0x2, 0xbf, 0xe9,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xd0, 0x7, 0xe4,
    0x40, 0x9, 0xb0, 0x0, 0xcf, 0xff, 0x70, 0x1a,
    0xc2, 0x10, 0xa, 0xb0, 0x0, 0xa, 0xb0, 0x0,
    0xa, 0xb0, 0x0, 0xa, 0xb0, 0x0, 0xa, 0xb0,
    0x0,

    /* U+0067 "g" */
    0x2, 0xbf, 0xc5, 0xf0, 0xd, 0xc5, 0x7e, 0xf0,
    0x4f, 0x20, 0x8, 0xf0, 0x6f, 0x0, 0x5, 0xf0,
    0x4f, 0x20, 0x7, 0xf0, 0xd, 0xc4, 0x6e, 0xf0,
    0x2, 0xbf, 0xc8, 0xf0, 0x1, 0x0, 0x8, 0xe0,
    0xe, 0x94, 0x6f, 0x80, 0x4, 0xcf, 0xe7, 0x0,

    /* U+0068 "h" */
    0xf, 0x40, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0, 0xf7, 0xdf, 0xb1, 0xf, 0xe6,
    0x6f, 0x80, 0xf6, 0x0, 0xab, 0xf, 0x40, 0x8,
    0xc0, 0xf4, 0x0, 0x8c, 0xf, 0x40, 0x8, 0xc0,
    0xf4, 0x0, 0x8c,

    /* U+0069 "i" */
    0x1e, 0x40, 0xa2, 0x0, 0x0, 0xf4, 0xf, 0x40,
    0xf4, 0xf, 0x40, 0xf4, 0xf, 0x40, 0xf4,

    /* U+006A "j" */
    0x0, 0x1e, 0x40, 0x0, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0xf4, 0x0, 0xf, 0x40, 0x0, 0xf4, 0x0,
    0xf, 0x40, 0x0, 0xf4, 0x0, 0xf, 0x40, 0x0,
    0xf4, 0x0, 0x1f, 0x41, 0x58, 0xf1, 0x5e, 0xe5,
    0x0,

    /* U+006B "k" */
    0xf, 0x40, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0xf, 0x40, 0x3f, 0x50,
    0xf, 0x42, 0xe7, 0x0, 0xf, 0x5d, 0xa0, 0x0,
    0xf, 0xef, 0xb0, 0x0, 0xf, 0xd4, 0xf6, 0x0,
    0xf, 0x40, 0x8f, 0x10, 0xf, 0x40, 0xd, 0xb0,

    /* U+006C "l" */
    0xf, 0x40, 0xf4, 0xf, 0x40, 0xf4, 0xf, 0x40,
    0xf4, 0xf, 0x40, 0xf4, 0xf, 0x40, 0xf4,

    /* U+006D "m" */
    0xf, 0x7e, 0xf7, 0x4d, 0xe6, 0x0, 0xfd, 0x28,
    0xfc, 0x27, 0xf1, 0xf, 0x60, 0x1f, 0x60, 0xf,
    0x40, 0xf4, 0x0, 0xf5, 0x0, 0xf5, 0xf, 0x40,
    0xf, 0x50, 0xf, 0x50, 0xf4, 0x0, 0xf5, 0x0,
    0xf5, 0xf, 0x40, 0xf, 0x50, 0xf, 0x50,

    /* U+006E "n" */
    0xf, 0x6d, 0xfb, 0x10, 0xfd, 0x33, 0xe8, 0xf,
    0x60, 0x9, 0xb0, 0xf4, 0x0, 0x8c, 0xf, 0x40,
    0x8, 0xc0, 0xf4, 0x0, 0x8c, 0xf, 0x40, 0x8,
    0xc0,

    /* U+006F "o" */
    0x1, 0xaf, 0xe8, 0x0, 0xd, 0xd5, 0x6f, 0x80,
    0x4f, 0x20, 0x7, 0xf0, 0x6f, 0x0, 0x4, 0xf2,
    0x4f, 0x20, 0x7, 0xf0, 0xd, 0xc4, 0x6e, 0x80,
    0x1, 0xaf, 0xe8, 0x0,

    /* U+0070 "p" */
    0xf, 0x6d, 0xfc, 0x20, 0xf, 0xe4, 0x2b, 0xd0,
    0xf, 0x70, 0x2, 0xf4, 0xf, 0x50, 0x0, 0xf6,
    0xf, 0x70, 0x2, 0xf4, 0xf, 0xf6, 0x4c, 0xd0,
    0xf, 0x7c, 0xfc, 0x20, 0xf, 0x40, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0,

    /* U+0071 "q" */
    0x2, 0xcf, 0xc5, 0xf0, 0xe, 0xc4, 0x6e, 0xf0,
    0x4f, 0x20, 0x7, 0xf0, 0x6f, 0x0, 0x5, 0xf0,
    0x4f, 0x20, 0x7, 0xf0, 0xe, 0xc4, 0x6f, 0xf0,
    0x2, 0xcf, 0xc7, 0xf0, 0x0, 0x0, 0x4, 0xf0,
    0x0, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x4, 0xf0,

    /* U+0072 "r" */
    0xf, 0x7e, 0xd0, 0xf, 0xe3, 0x20, 0xf, 0x60,
    0x0, 0xf, 0x40, 0x0, 0xf, 0x40, 0x0, 0xf,
    0x40, 0x0, 0xf, 0x40, 0x0,

    /* U+0073 "s" */
    0x7, 0xef, 0xa0, 0x5f, 0x54, 0xd4, 0x5f, 0x60,
    0x0, 0x6, 0xdf, 0xa1, 0x1, 0x2, 0xe8, 0x7c,
    0x34, 0xe8, 0xa, 0xef, 0xa0,

    /* U+0074 "t" */
    0xb, 0x90, 0x0, 0xb9, 0x0, 0xef, 0xff, 0x92,
    0xca, 0x21, 0xb, 0x90, 0x0, 0xb9, 0x0, 0xb,
    0x90, 0x0, 0x9e, 0x43, 0x2, 0xcf, 0xa0,

    /* U+0075 "u" */
    0x2f, 0x30, 0xa, 0xb2, 0xf3, 0x0, 0xab, 0x2f,
    0x30, 0xa, 0xb2, 0xf3, 0x0, 0xab, 0x1f, 0x40,
    0xc, 0xb0, 0xeb, 0x26, 0xfb, 0x4, 0xdf, 0xb9,
    0xb0,

    /* U+0076 "v" */
    0xbc, 0x0, 0xc, 0x94, 0xf2, 0x1, 0xf3, 0xd,
    0x80, 0x7d, 0x0, 0x7e, 0xd, 0x60, 0x1, 0xf7,
    0xf1, 0x0, 0xa, 0xfa, 0x0, 0x0, 0x3f, 0x30,
    0x0,

    /* U+0077 "w" */
    0xba, 0x0, 0x7e, 0x0, 0x3f, 0x5, 0xf0, 0xc,
    0xf3, 0x8, 0xa0, 0xf, 0x51, 0xfc, 0x90, 0xd5,
    0x0, 0xaa, 0x6b, 0x5e, 0x2f, 0x0, 0x4, 0xfb,
    0x60, 0xfb, 0xa0, 0x0, 0xe, 0xf1, 0xa, 0xf4,
    0x0, 0x0, 0x9b, 0x0, 0x4f, 0x0, 0x0,

    /* U+0078 "x" */
    0x8f, 0x20, 0x5f, 0x30, 0xcc, 0x1e, 0x70, 0x2,
    0xfe, 0xc0, 0x0, 0x9, 0xf3, 0x0, 0x2, 0xfe,
    0xc0, 0x0, 0xca, 0x2f, 0x70, 0x8e, 0x10, 0x6f,
    0x30,

    /* U+0079 "y" */
    0xbc, 0x0, 0xb, 0xa5, 0xf2, 0x1, 0xf4, 0xe,
    0x80, 0x6d, 0x0, 0x7e, 0xc, 0x70, 0x1, 0xf7,
    0xf1, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x4f, 0x50,
    0x0, 0x5, 0xe0, 0x0, 0x44, 0xe7, 0x0, 0xb,
    0xfa, 0x0, 0x0,

    /* U+007A "z" */
    0x6f, 0xff, 0xf9, 0x1, 0x18, 0xf2, 0x0, 0x2f,
    0x70, 0x0, 0xbc, 0x0, 0x6, 0xf2, 0x0, 0x2f,
    0x82, 0x21, 0x9f, 0xff, 0xfc,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x5, 0xeb, 0x0, 0xfa, 0x20,
    0x1f, 0x40, 0x1, 0xf4, 0x0, 0x2f, 0x30, 0x18,
    0xf1, 0x8, 0xf7, 0x0, 0x2a, 0xf1, 0x0, 0x2f,
    0x40, 0x1, 0xf4, 0x0, 0xf, 0x70, 0x0, 0xaf,
    0xb0, 0x0, 0x12,

    /* U+007C "|" */
    0x12, 0x5e, 0x5e, 0x5e, 0x5e, 0x5e, 0x5e, 0x5e,
    0x5e, 0x5e, 0x5e, 0x5e, 0x5e,

    /* U+007D "}" */
    0x0, 0x0, 0xb, 0xe5, 0x0, 0x2a, 0xe0, 0x0,
    0x4f, 0x10, 0x4, 0xf1, 0x0, 0x4f, 0x10, 0x1,
    0xf8, 0x10, 0x7, 0xf8, 0x1, 0xfa, 0x20, 0x4f,
    0x20, 0x4, 0xf1, 0x0, 0x8f, 0x0, 0xbf, 0x90,
    0x2, 0x10, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc2, 0x8b, 0x2f,
    0x17, 0xff, 0x40, 0x20, 0x1, 0x10,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0xf, 0x4a, 0x70, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0x2d, 0x20, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x33, 0x33, 0x33, 0xe8,
    0x33, 0x30, 0x0, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0xef, 0xff, 0xfa, 0xa9, 0x0, 0x0, 0x3,
    0x3c, 0x93, 0x28, 0xb0, 0x0, 0x0, 0x0, 0xc8,
    0x0, 0x5e, 0x0, 0x0, 0x0, 0xc, 0x80, 0x2,
    0xf2, 0xb, 0x20, 0x13, 0xdc, 0xbe, 0x2d, 0x80,
    0xf1, 0x4f, 0xfe, 0xb9, 0x60, 0x6f, 0x9d, 0x0,
    0x20, 0x0, 0x0, 0x0, 0xaf, 0x50,

    /* U+6A21 "模" */
    0x0, 0xe2, 0x0, 0xd3, 0xe, 0x30, 0x0, 0xe,
    0x26, 0xcf, 0xcc, 0xfc, 0xc0, 0x24, 0xf6, 0x56,
    0xc8, 0x6c, 0x76, 0x6, 0xcf, 0xd6, 0xaa, 0xaa,
    0xaa, 0x40, 0x4, 0xf4, 0xf, 0x55, 0x55, 0xb6,
    0x0, 0xbf, 0xe2, 0xfc, 0xcc, 0xce, 0x60, 0x3e,
    0xe8, 0x5f, 0x87, 0x77, 0xc6, 0xc, 0x7e, 0x20,
    0x55, 0x9d, 0x55, 0x20, 0x50, 0xe2, 0xbf, 0xff,
    0xff, 0xff, 0x0, 0xe, 0x20, 0x4, 0xfe, 0x80,
    0x0, 0x0, 0xe2, 0x29, 0xf6, 0x2d, 0xb4, 0x0,
    0xe, 0x2b, 0xa2, 0x0, 0x18, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xaf, 0xf0, 0x0, 0x0,
    0x3, 0x7c, 0xff, 0xff, 0xf0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xb, 0xff, 0xff,
    0xfe, 0xac, 0xf0, 0x0, 0xb, 0xff, 0xd8, 0x30,
    0x9, 0xf0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x9,
    0xf0, 0x0, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf0, 0x0,
    0xb, 0xe0, 0x0, 0x3d, 0xff, 0xf0, 0x3, 0x6c,
    0xe0, 0x0, 0xcf, 0xff, 0xf0, 0xaf, 0xff, 0xe0,
    0x0, 0x5f, 0xff, 0x80, 0xdf, 0xff, 0xd0, 0x0,
    0x0, 0x21, 0x0, 0x2a, 0xda, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0x80, 0x9c, 0xcc, 0xcc, 0xcc, 0x40, 0x8f, 0xdf,
    0xb7, 0x77, 0x77, 0xfe, 0xdf, 0xd0, 0xc7, 0x0,
    0x0, 0xd, 0x50, 0xde, 0x8e, 0x70, 0x0, 0x0,
    0xdb, 0x8e, 0xe5, 0xdb, 0x77, 0x77, 0x7f, 0x95,
    0xed, 0x1c, 0xeb, 0xbb, 0xbb, 0xf6, 0x1d, 0xfc,
    0xf7, 0x0, 0x0, 0xd, 0xdc, 0xfd, 0xc, 0x70,
    0x0, 0x0, 0xd5, 0xd, 0xfa, 0xe9, 0x33, 0x33,
    0x3e, 0xca, 0xfc, 0x3d, 0xff, 0xff, 0xff, 0xf8,
    0x3c,

    /* U+F00B "" */
    0x35, 0x52, 0x5, 0x55, 0x55, 0x55, 0x3f, 0xff,
    0xc6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x6f,
    0xff, 0xff, 0xff, 0xfc, 0xee, 0x94, 0xee, 0xee,
    0xee, 0xec, 0x34, 0x42, 0x4, 0x44, 0x44, 0x44,
    0x3f, 0xff, 0xc6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x6f, 0xff, 0xff, 0xff, 0xfc, 0xee, 0x94,
    0xee, 0xee, 0xee, 0xec, 0x34, 0x42, 0x4, 0x44,
    0x44, 0x44, 0x3f, 0xff, 0xc6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x6f, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xa4, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x90, 0x50, 0x0, 0x0, 0x2e,
    0xff, 0x90, 0xaf, 0xc0, 0x0, 0x2e, 0xff, 0x90,
    0xc, 0xff, 0xc0, 0x2e, 0xff, 0x90, 0x0, 0x1c,
    0xff, 0xce, 0xff, 0x90, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x90, 0x0, 0x0,
    0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x6,
    0xf8, 0xdf, 0xf5, 0x6, 0xff, 0xc2, 0xef, 0xf9,
    0xff, 0xd1, 0x2, 0xef, 0xff, 0xd1, 0x0, 0x8,
    0xff, 0xf7, 0x0, 0x6, 0xff, 0xff, 0xf5, 0x6,
    0xff, 0xe4, 0xef, 0xf5, 0xef, 0xe1, 0x2, 0xef,
    0xd4, 0xa1, 0x0, 0x2, 0xb4,

    /* U+F011 "" */
    0x0, 0x0, 0x2, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xbf, 0x40, 0x30, 0x0, 0x1, 0xcf, 0x1b,
    0xf4, 0x7f, 0x80, 0x0, 0xbf, 0xd0, 0xbf, 0x45,
    0xff, 0x40, 0x4f, 0xe1, 0xb, 0xf4, 0x7, 0xfd,
    0x9, 0xf8, 0x0, 0xbf, 0x40, 0xe, 0xf2, 0xcf,
    0x40, 0xb, 0xf4, 0x0, 0xbf, 0x4b, 0xf4, 0x0,
    0x9e, 0x30, 0xb, 0xf4, 0x9f, 0x80, 0x0, 0x0,
    0x0, 0xff, 0x23, 0xff, 0x20, 0x0, 0x0, 0x8f,
    0xc0, 0xa, 0xfe, 0x40, 0x1, 0x8f, 0xf4, 0x0,
    0xc, 0xff, 0xed, 0xff, 0xf6, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x10, 0x3f,
    0xff, 0x70, 0x11, 0x0, 0x7f, 0xbf, 0xff, 0xff,
    0xce, 0xb0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x53, 0xff, 0xff, 0x82, 0x5f, 0xff, 0xf7, 0x3,
    0xff, 0xc0, 0x0, 0x7f, 0xf8, 0x0, 0x3f, 0xfa,
    0x0, 0x6, 0xff, 0x70, 0x2c, 0xff, 0xf4, 0x1,
    0xdf, 0xfe, 0x42, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xf6, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x5, 0x7, 0xff, 0xfa, 0x13, 0x20, 0x0, 0x0,
    0xf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x46,
    0x50, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x25, 0x0, 0x36, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xfc, 0x1a, 0xf6, 0x0, 0x0,
    0x0, 0x7f, 0xe9, 0xfe, 0xcf, 0x60, 0x0, 0x0,
    0xaf, 0xc3, 0x64, 0xff, 0xf6, 0x0, 0x1, 0xcf,
    0xa3, 0xef, 0xa3, 0xdf, 0x90, 0x3, 0xef, 0x75,
    0xff, 0xff, 0xc3, 0xcf, 0xb0, 0xdf, 0x48, 0xff,
    0xff, 0xff, 0xe3, 0x9f, 0x72, 0x27, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x50, 0x0, 0x9f, 0xff, 0xcb,
    0xef, 0xff, 0x30, 0x0, 0x9, 0xff, 0xf1, 0x7,
    0xff, 0xf3, 0x0, 0x0, 0x9f, 0xff, 0x10, 0x7f,
    0xff, 0x30, 0x0, 0x7, 0xff, 0xe0, 0x6, 0xff,
    0xf2, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x4, 0x54, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x47, 0x8f, 0xff, 0x87,
    0x40, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0, 0x0, 0x46,
    0x66, 0x3a, 0xfa, 0x36, 0x66, 0x50, 0xff, 0xff,
    0xf4, 0x54, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xb8, 0xf2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa0,

    /* U+F01C "" */
    0x0, 0x8, 0xaa, 0xaa, 0xaa, 0xa4, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0xdf,
    0x10, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x8f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x3f, 0xfe, 0xee,
    0x40, 0x0, 0x9e, 0xee, 0xf9, 0xff, 0xff, 0xfc,
    0x44, 0x5f, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x7, 0x90, 0x0,
    0x6d, 0xff, 0xfb, 0x40, 0xcf, 0x0, 0xbf, 0xfd,
    0xbd, 0xff, 0xac, 0xf0, 0xbf, 0xd3, 0x0, 0x3,
    0xcf, 0xff, 0x4f, 0xd1, 0x0, 0x7, 0xaa, 0xff,
    0xf9, 0xf5, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x1,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x4f, 0x9f, 0xff, 0xab, 0x70, 0x0, 0xd,
    0xf4, 0xff, 0xfb, 0x20, 0x0, 0x3c, 0xfb, 0xf,
    0xcb, 0xff, 0xca, 0xcf, 0xfc, 0x10, 0xfc, 0x5,
    0xcf, 0xff, 0xd7, 0x0, 0x9, 0x70, 0x0, 0x12,
    0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x5, 0x20, 0x0, 0x6, 0xf7, 0x1,
    0x16, 0xff, 0x8f, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0x8a, 0xcc, 0xef, 0xf8, 0x0, 0x2, 0xef, 0x80,
    0x0, 0x2, 0xe7, 0x0, 0x0, 0x1, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0, 0x6f,
    0x70, 0x0, 0x1, 0x16, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x82, 0xb1, 0xff, 0xff, 0xff, 0x80,
    0xc9, 0xff, 0xff, 0xff, 0x80, 0x9b, 0xff, 0xff,
    0xff, 0x82, 0xf3, 0xbd, 0xdf, 0xff, 0x80, 0x10,
    0x0, 0x3, 0xef, 0x80, 0x0, 0x0, 0x0, 0x3e,
    0x70, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xc2, 0x0, 0x0,
    0x0, 0x5, 0x20, 0x0, 0xb, 0xe1, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0xb7, 0xc, 0xb0, 0x1, 0x16,
    0xff, 0x80, 0x5, 0xf5, 0x2f, 0x3f, 0xff, 0xff,
    0xf8, 0x2c, 0x17, 0xd0, 0xb8, 0xff, 0xff, 0xff,
    0x80, 0xba, 0x2f, 0x18, 0xbf, 0xff, 0xff, 0xf8,
    0x9, 0xb1, 0xf2, 0x7b, 0xff, 0xff, 0xff, 0x82,
    0xf3, 0x5e, 0xa, 0x9a, 0xcc, 0xef, 0xf8, 0x0,
    0x2e, 0x80, 0xe4, 0x0, 0x2, 0xef, 0x80, 0xc,
    0xa0, 0x8d, 0x0, 0x0, 0x2, 0xe7, 0x0, 0x10,
    0x7f, 0x30, 0x0, 0x0, 0x1, 0x0, 0x0, 0x4f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0,

    /* U+F03E "" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xf9,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xd, 0xff, 0xf8,
    0xaf, 0xff, 0xff, 0x9b, 0xff, 0xf8, 0x0, 0xaf,
    0xff, 0xff, 0x6d, 0xf8, 0x0, 0x0, 0xcf, 0xff,
    0x40, 0x17, 0x0, 0x0, 0xa, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0xaa, 0xaa, 0xaa,
    0xaa, 0xad, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F043 "" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x6f, 0x50,
    0x0, 0x0, 0xc, 0xfb, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0, 0x8f,
    0xff, 0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0x2a,
    0xff, 0xff, 0xff, 0xf9, 0xef, 0xef, 0xff, 0xff,
    0xdf, 0xb7, 0xff, 0xff, 0xfe, 0xce, 0x2d, 0xff,
    0xff, 0xb5, 0xfc, 0x36, 0xff, 0xf3, 0x7, 0xff,
    0xff, 0xf6, 0x0, 0x2, 0x89, 0x72, 0x0,

    /* U+F048 "" */
    0x6, 0x40, 0x0, 0x2, 0x42, 0xfa, 0x0, 0x3,
    0xef, 0x2f, 0xa0, 0x4, 0xff, 0xf2, 0xfa, 0x5,
    0xff, 0xff, 0x2f, 0xa6, 0xff, 0xff, 0xf2, 0xfe,
    0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xf2,
    0xfc, 0xdf, 0xff, 0xff, 0x2f, 0xa1, 0xdf, 0xff,
    0xf2, 0xfa, 0x1, 0xcf, 0xff, 0x2f, 0xa0, 0x0,
    0xbf, 0xf2, 0xf9, 0x0, 0x0, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x48, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x26, 0x66, 0x10, 0x16, 0x66, 0x30, 0xef, 0xff,
    0xc0, 0xaf, 0xff, 0xf1, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2,
    0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff,
    0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xf2, 0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2,
    0xff, 0xff, 0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff,
    0xe0, 0xbf, 0xff, 0xf2, 0xff, 0xff, 0xd0, 0xbf,
    0xff, 0xf2, 0xaf, 0xff, 0x80, 0x6e, 0xff, 0xc0,

    /* U+F04D "" */
    0x26, 0x66, 0x66, 0x66, 0x66, 0x50, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xd1,

    /* U+F051 "" */
    0x5, 0x0, 0x0, 0x6, 0x45, 0xfb, 0x0, 0x1,
    0xfc, 0x6f, 0xfc, 0x10, 0x1f, 0xc6, 0xff, 0xfd,
    0x11, 0xfc, 0x6f, 0xff, 0xfd, 0x3f, 0xc6, 0xff,
    0xff, 0xff, 0xfc, 0x6f, 0xff, 0xff, 0xff, 0xc6,
    0xff, 0xff, 0xfa, 0xfc, 0x6f, 0xff, 0xf7, 0x1f,
    0xc6, 0xff, 0xf6, 0x1, 0xfc, 0x6f, 0xf5, 0x0,
    0x1f, 0xc3, 0xe4, 0x0, 0x0, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x8e, 0xee,
    0xee, 0xee, 0xee, 0xc1, 0x2, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xe3,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0xb, 0xff, 0x30,
    0x0, 0xbf, 0xf3, 0x0, 0xb, 0xff, 0x30, 0x0,
    0x2f, 0xf9, 0x0, 0x0, 0x6, 0xff, 0x80, 0x0,
    0x0, 0x6f, 0xf8, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0, 0x5, 0xb0,

    /* U+F054 "" */
    0x1, 0x0, 0x0, 0x0, 0x1e, 0xc0, 0x0, 0x0,
    0x1e, 0xfc, 0x0, 0x0, 0x2, 0xef, 0xc0, 0x0,
    0x0, 0x2e, 0xfc, 0x0, 0x0, 0x2, 0xef, 0xc0,
    0x0, 0x0, 0x7f, 0xf4, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6f, 0xf7, 0x0, 0x6, 0xff, 0x70, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0xa, 0x70, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x17, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x36, 0x66, 0xbf, 0xf6, 0x66, 0x50,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0x90, 0x0, 0x0,

    /* U+F068 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x35, 0x55, 0x55, 0x55, 0x55, 0x40,

    /* U+F06E "" */
    0x0, 0x0, 0x5a, 0xcd, 0xc8, 0x20, 0x0, 0x0,
    0x2, 0xdf, 0xd7, 0x69, 0xff, 0x90, 0x0, 0x4,
    0xff, 0xa0, 0x1, 0x2, 0xef, 0xc1, 0x2, 0xff,
    0xf1, 0x2, 0xfc, 0x16, 0xff, 0xb0, 0xcf, 0xfb,
    0x5, 0xaf, 0xf9, 0x1f, 0xff, 0x6d, 0xff, 0xb1,
    0xff, 0xff, 0xb1, 0xff, 0xf7, 0x4f, 0xfe, 0xb,
    0xff, 0xf5, 0x5f, 0xfc, 0x0, 0x6f, 0xf9, 0x8,
    0xa5, 0x1d, 0xfd, 0x20, 0x0, 0x5e, 0xfb, 0x53,
    0x6e, 0xfb, 0x10, 0x0, 0x0, 0x7, 0xce, 0xfe,
    0xa4, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xf9, 0x5, 0xac, 0xdb, 0x71, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xfd, 0x76, 0xaf, 0xf7,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x31, 0x30, 0x4f,
    0xfa, 0x0, 0x0, 0x32, 0x4, 0xef, 0xbf, 0xc0,
    0x9f, 0xf8, 0x0, 0xe, 0xf5, 0x2, 0xcf, 0xff,
    0x64, 0xff, 0xf3, 0x1, 0xff, 0xf6, 0x0, 0x9f,
    0xf8, 0x4f, 0xff, 0x40, 0x6, 0xff, 0xc0, 0x0,
    0x6f, 0xeb, 0xff, 0xa0, 0x0, 0x9, 0xff, 0x60,
    0x0, 0x3e, 0xff, 0xc0, 0x0, 0x0, 0x7, 0xff,
    0xa4, 0x30, 0x1b, 0xfb, 0x10, 0x0, 0x0, 0x2,
    0x8d, 0xff, 0x80, 0x8, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x70,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0x1a, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf4, 0xa, 0xff, 0x60, 0x0,
    0x0, 0x5, 0xff, 0xf5, 0xa, 0xff, 0xe1, 0x0,
    0x0, 0xe, 0xff, 0xf6, 0xb, 0xff, 0xf9, 0x0,
    0x0, 0x8f, 0xff, 0xfd, 0x8f, 0xff, 0xff, 0x20,
    0x2, 0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xb0,
    0xa, 0xff, 0xff, 0xf7, 0x1c, 0xff, 0xff, 0xf4,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x91,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xa0, 0xef, 0xfa, 0x0,
    0x5, 0xff, 0xff, 0xaf, 0xff, 0xf9, 0x4, 0xff,
    0xff, 0xfb, 0x11, 0x3f, 0xa4, 0xff, 0x84, 0xfc,
    0x0, 0x0, 0x33, 0xff, 0x90, 0x19, 0x0, 0x0,
    0x2, 0xef, 0xa2, 0x1, 0x70, 0x0, 0x2, 0xef,
    0xa4, 0xf6, 0x3f, 0xa0, 0xef, 0xff, 0xb0, 0x5f,
    0xff, 0xff, 0xaf, 0xff, 0xc0, 0x0, 0x6f, 0xff,
    0xfb, 0x11, 0x10, 0x0, 0x0, 0x14, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xa0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x5f, 0xfa, 0xff, 0xa0, 0x0,
    0x5, 0xff, 0x90, 0x3f, 0xfa, 0x0, 0x4f, 0xf9,
    0x0, 0x3, 0xff, 0xa0, 0xbf, 0x90, 0x0, 0x0,
    0x3f, 0xf1, 0x15, 0x0, 0x0, 0x0, 0x3, 0x30,

    /* U+F078 "" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x10, 0x9f, 0x50,
    0x0, 0x0, 0x1d, 0xe1, 0x8f, 0xf5, 0x0, 0x1,
    0xdf, 0xd0, 0x8, 0xff, 0x50, 0x1d, 0xfd, 0x10,
    0x0, 0x8f, 0xf6, 0xdf, 0xd1, 0x0, 0x0, 0x8,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x10, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x7d, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfd, 0x18, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x6f, 0xff, 0xfd, 0x4a, 0xaa, 0xaa, 0xfb,
    0x0, 0xc, 0xda, 0xf7, 0xf4, 0x0, 0x0, 0xe,
    0xb0, 0x0, 0x0, 0x9f, 0x11, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x9f, 0x10, 0x0, 0x2,
    0xe6, 0xeb, 0x9d, 0x0, 0x9, 0xf9, 0x99, 0x99,
    0x4d, 0xff, 0xff, 0xa0, 0x0, 0x7f, 0xff, 0xff,
    0xfb, 0x1d, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x6b, 0xcc, 0xc7, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf8, 0x22, 0x22, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x69, 0x9f, 0xff, 0x99,
    0x60, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x46,
    0x66, 0x2f, 0xff, 0x26, 0x66, 0x50, 0xff, 0xff,
    0x46, 0x76, 0x4f, 0xff, 0xf2, 0xff, 0xff, 0xfc,
    0xbc, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xb8, 0xf2, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x1, 0x50, 0x0, 0x4f, 0xfd, 0x0, 0x3,
    0xaf, 0xf8, 0x7, 0xff, 0xf2, 0x0, 0xe, 0xff,
    0xff, 0xef, 0xfe, 0x30, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x8, 0xff, 0xff, 0xd5,
    0x0, 0x0, 0x0, 0x2, 0x98, 0x62, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0C4 "" */
    0x5, 0x84, 0x0, 0x0, 0x1, 0x0, 0x8f, 0xff,
    0x60, 0x0, 0xaf, 0xf3, 0xec, 0x2e, 0xc0, 0x1c,
    0xff, 0xa0, 0xde, 0x9f, 0xd1, 0xdf, 0xfa, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x2d,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xf7,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0x60, 0x0,
    0xcf, 0xaf, 0xd2, 0xef, 0xf6, 0x0, 0xfb, 0xd,
    0xd0, 0x2e, 0xff, 0x60, 0xbf, 0xef, 0x80, 0x2,
    0xef, 0xf3, 0x19, 0xc8, 0x0, 0x0, 0x16, 0x40,

    /* U+F0C5 "" */
    0x0, 0x2, 0x66, 0x66, 0x12, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x3f, 0x40, 0x0, 0xc, 0xff, 0xff,
    0x3f, 0xf2, 0xdf, 0x6c, 0xff, 0xff, 0x53, 0x31,
    0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0x7c, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x7c,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0x7b, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xc2, 0x33, 0x33, 0x33, 0x20,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x8a, 0xaa,
    0xaa, 0xa9, 0x0, 0x0,

    /* U+F0C7 "" */
    0x49, 0x99, 0x99, 0x99, 0x60, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xfa, 0x0, 0x0, 0x1,
    0xff, 0x80, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xf5, 0xfe, 0xaa,
    0xaa, 0xaa, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0x80, 0x4f, 0xff, 0xf5,
    0xff, 0xff, 0x10, 0xc, 0xff, 0xf5, 0xff, 0xff,
    0x50, 0x1e, 0xff, 0xf5, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xf5, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xd1,

    /* U+F0C9 "" */
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0E0 "" */
    0x6b, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x95, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x65, 0xf9, 0x3d, 0xff, 0xff, 0xfd, 0x39,
    0xff, 0xfd, 0x39, 0xff, 0xf9, 0x3d, 0xff, 0xff,
    0xff, 0x65, 0xa4, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xb7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+F0E7 "" */
    0x0, 0x55, 0x55, 0x20, 0x0, 0x3, 0xff, 0xff,
    0x90, 0x0, 0x5, 0xff, 0xff, 0x40, 0x0, 0x8,
    0xff, 0xff, 0x0, 0x0, 0xa, 0xff, 0xfc, 0x44,
    0x40, 0xc, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff,
    0xff, 0xff, 0x80, 0xd, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0xbf,
    0xc0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x3, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x0,
    0x0, 0x0, 0x3, 0x50, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x6e,
    0xf7, 0x54, 0x0, 0x0, 0xff, 0xf9, 0x8f, 0xff,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xff, 0xfa, 0x24, 0x44, 0x1, 0x0, 0xff, 0xf4,
    0xef, 0xff, 0x3e, 0x20, 0xff, 0xf4, 0xff, 0xff,
    0x3f, 0xe2, 0xff, 0xf4, 0xff, 0xff, 0x43, 0x31,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xf6, 0x23, 0x30, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x7a, 0xaa, 0xaa, 0xa2,

    /* U+F0F3 "" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0x0, 0x0, 0x7, 0xef, 0xfa,
    0x10, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xd0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0x30, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x13, 0x33, 0x33, 0x33, 0x33, 0x20,
    0x0, 0x0, 0xcf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x29, 0x50, 0x0, 0x0,

    /* U+F11C "" */
    0x5a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xfa,
    0xd, 0x1, 0xb0, 0xd0, 0xa2, 0xf, 0xaf, 0xeb,
    0xfb, 0xcf, 0xbf, 0xbe, 0xcb, 0xfa, 0xff, 0xe1,
    0x98, 0x1c, 0x1c, 0x15, 0xff, 0xaf, 0xfd, 0x8,
    0x70, 0xb0, 0xb0, 0x3f, 0xfa, 0xff, 0xdf, 0xdd,
    0xdd, 0xdd, 0xfd, 0xdf, 0xaf, 0xa0, 0xd0, 0x0,
    0x0, 0xa, 0x20, 0xfa, 0xfe, 0xaf, 0xaa, 0xaa,
    0xaa, 0xeb, 0xaf, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x1, 0x8f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xff, 0x50, 0x0, 0x3,
    0xbf, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x3c, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x9e, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x76, 0x0, 0x0,
    0x0,

    /* U+F15B "" */
    0x34, 0x44, 0x43, 0x10, 0x0, 0xff, 0xff, 0xfb,
    0x8a, 0x0, 0xff, 0xff, 0xfb, 0x8f, 0xa0, 0xff,
    0xff, 0xfb, 0x7f, 0xf8, 0xff, 0xff, 0xfc, 0x11,
    0x11, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbd, 0xdd, 0xdd, 0xdd, 0xd8,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x35, 0x54, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0xff, 0xd8, 0x10,
    0x0, 0x4, 0xef, 0xff, 0xdb, 0xbc, 0xff, 0xff,
    0x70, 0x8, 0xff, 0xd6, 0x10, 0x0, 0x0, 0x4b,
    0xff, 0xb0, 0xbf, 0x70, 0x0, 0x13, 0x32, 0x0,
    0x5, 0xed, 0x10, 0x30, 0x6, 0xdf, 0xff, 0xfe,
    0x80, 0x1, 0x10, 0x0, 0xb, 0xff, 0xfc, 0xce,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0xbe, 0x60, 0x0,
    0x4, 0xde, 0x10, 0x0, 0x0, 0x0, 0x10, 0x3,
    0x40, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xac, 0x30, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x88, 0x88, 0x65,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x99, 0x99,
    0x98, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x88, 0x30, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0x70,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x99, 0x94,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x88, 0x88, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xff, 0xff, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x99, 0x90, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x48, 0x85, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa9, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x9f, 0xfa, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa5, 0x99, 0x60, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xf, 0xea, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf,
    0x90, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xf1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xaf, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xfa, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x5c, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xef, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0x5c, 0x50, 0x0,
    0x0, 0x0, 0x64, 0x0, 0xa5, 0x0, 0x0, 0x0,
    0x10, 0x0, 0xbf, 0xf6, 0x4e, 0x21, 0x11, 0x11,
    0x17, 0xd3, 0xf, 0xff, 0xec, 0xce, 0xec, 0xcc,
    0xcc, 0xef, 0xe1, 0x7f, 0xe3, 0x0, 0x2d, 0x0,
    0x0, 0x6, 0x80, 0x0, 0x10, 0x0, 0x0, 0x97,
    0xa, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xe8, 0x0, 0x0, 0xdf, 0xf8, 0xcf, 0xf8,
    0x0, 0x7f, 0xff, 0x81, 0xdf, 0xf1, 0xc, 0xfa,
    0xf8, 0x52, 0xdf, 0x60, 0xff, 0x64, 0x78, 0x3a,
    0xf8, 0xf, 0xff, 0x50, 0x8, 0xff, 0xa1, 0xff,
    0xff, 0x12, 0xff, 0xfa, 0xf, 0xff, 0x41, 0x15,
    0xff, 0xa0, 0xef, 0x46, 0x88, 0x48, 0xf8, 0xb,
    0xfc, 0xf8, 0x42, 0xef, 0x50, 0x5f, 0xff, 0x82,
    0xef, 0xf1, 0x0, 0xaf, 0xfa, 0xef, 0xf6, 0x0,
    0x0, 0x5a, 0xdc, 0xa4, 0x0,

    /* U+F2ED "" */
    0x0, 0x1, 0x56, 0x63, 0x0, 0x0, 0x78, 0x8b,
    0xff, 0xfe, 0x88, 0x82, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x3f, 0xfc,
    0xfc, 0xfe, 0xdf, 0x90, 0x3f, 0xd5, 0xf4, 0xeb,
    0x7f, 0x90, 0x3f, 0xd5, 0xf4, 0xeb, 0x7f, 0x90,
    0x3f, 0xd5, 0xf4, 0xeb, 0x7f, 0x90, 0x3f, 0xd5,
    0xf4, 0xeb, 0x7f, 0x90, 0x3f, 0xd5, 0xf4, 0xeb,
    0x7f, 0x90, 0x3f, 0xe6, 0xf5, 0xec, 0x8f, 0x90,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x7, 0x9a,
    0xaa, 0xaa, 0xa9, 0x10,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x3, 0xc3, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x3e,
    0xfd, 0x3d, 0xf5, 0x0, 0x0, 0x3, 0xef, 0xff,
    0xd3, 0x50, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0x60, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x86, 0x30, 0x0, 0x0,
    0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x8b, 0xcc, 0xcc, 0xcc, 0xcc, 0xb6,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0xbf, 0xff, 0xf8, 0xbf, 0xf6, 0xef,
    0xff, 0x40, 0xbf, 0xff, 0xff, 0x10, 0xa4, 0x9,
    0xff, 0xf4, 0xaf, 0xff, 0xff, 0xfd, 0x10, 0x7,
    0xff, 0xff, 0x4d, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xbf, 0xff, 0xf4, 0x1d, 0xff, 0xff, 0xf4, 0x6,
    0x10, 0xbf, 0xff, 0x40, 0x1d, 0xff, 0xff, 0x47,
    0xfd, 0x2b, 0xff, 0xf4, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0,

    /* U+F7C2 "" */
    0x0, 0x8, 0x99, 0x99, 0x70, 0x0, 0xbf, 0xff,
    0xff, 0xf8, 0xa, 0xd0, 0xd0, 0xd0, 0xeb, 0x9f,
    0xd0, 0xd0, 0xd0, 0xeb, 0xff, 0xd1, 0xd1, 0xd1,
    0xeb, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xef, 0xff, 0xff, 0xff,
    0xfa, 0x5c, 0xdd, 0xdd, 0xdd, 0xb2,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0xf, 0xf0, 0xa, 0xff, 0x21,
    0x11, 0x11, 0x1f, 0xf0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x8, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 56, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 51, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15, .adv_w = 78, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 25, .adv_w = 137, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 70, .adv_w = 121, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 126, .adv_w = 166, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 181, .adv_w = 150, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 236, .adv_w = 42, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 240, .adv_w = 74, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 273, .adv_w = 74, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 299, .adv_w = 92, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 317, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 342, .adv_w = 53, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 350, .adv_w = 102, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 356, .adv_w = 50, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 359, .adv_w = 84, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 389, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 424, .adv_w = 121, .box_w = 5, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 449, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 484, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 519, .adv_w = 121, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 559, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 594, .adv_w = 121, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 634, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 669, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 704, .adv_w = 121, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 739, .adv_w = 55, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 750, .adv_w = 57, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 765, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 790, .adv_w = 121, .box_w = 7, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 808, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 833, .adv_w = 94, .box_w = 7, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 868, .adv_w = 204, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 953, .adv_w = 142, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 998, .adv_w = 137, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1038, .adv_w = 137, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1083, .adv_w = 151, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1123, .adv_w = 125, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1158, .adv_w = 119, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1193, .adv_w = 147, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1238, .adv_w = 157, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1278, .adv_w = 57, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1288, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1318, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1363, .adv_w = 119, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1398, .adv_w = 181, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1448, .adv_w = 155, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1488, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1538, .adv_w = 126, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1573, .adv_w = 160, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1639, .adv_w = 136, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1679, .adv_w = 120, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1719, .adv_w = 121, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1759, .adv_w = 154, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1804, .adv_w = 141, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1849, .adv_w = 204, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1914, .adv_w = 141, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1959, .adv_w = 132, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2004, .adv_w = 121, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2044, .adv_w = 74, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2072, .adv_w = 84, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2102, .adv_w = 74, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2130, .adv_w = 102, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2145, .adv_w = 91, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2151, .adv_w = 66, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 2155, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2180, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2220, .adv_w = 103, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2245, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2285, .adv_w = 114, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2310, .adv_w = 74, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2343, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2383, .adv_w = 122, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2418, .adv_w = 52, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2433, .adv_w = 52, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2466, .adv_w = 113, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2506, .adv_w = 52, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2521, .adv_w = 179, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2560, .adv_w = 122, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2585, .adv_w = 124, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2613, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2653, .adv_w = 128, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2693, .adv_w = 82, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2714, .adv_w = 95, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2735, .adv_w = 80, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2758, .adv_w = 123, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2783, .adv_w = 111, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2808, .adv_w = 165, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2847, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2872, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2907, .adv_w = 99, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2928, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2963, .adv_w = 41, .box_w = 2, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2976, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3011, .adv_w = 121, .box_w = 7, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3025, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3103, .adv_w = 208, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3188, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3286, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3351, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3429, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3494, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3539, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3630, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3721, .adv_w = 234, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3811, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3909, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3984, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4075, .adv_w = 104, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4114, .adv_w = 156, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4169, .adv_w = 234, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4274, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4339, .adv_w = 143, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4402, .adv_w = 182, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4461, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4545, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4617, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4689, .adv_w = 182, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4748, .adv_w = 182, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4826, .adv_w = 130, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4874, .adv_w = 130, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4922, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4994, .adv_w = 182, .box_w = 12, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 5018, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5093, .adv_w = 260, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5212, .adv_w = 234, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5324, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5402, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5450, .adv_w = 182, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5498, .adv_w = 260, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5592, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5657, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5755, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5853, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5925, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6009, .adv_w = 182, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6081, .adv_w = 182, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6147, .adv_w = 208, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6212, .adv_w = 130, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6282, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6366, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6450, .adv_w = 234, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6525, .adv_w = 208, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6630, .adv_w = 156, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6700, .adv_w = 260, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6802, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6879, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6956, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7033, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7110, .adv_w = 260, .box_w = 17, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7187, .adv_w = 260, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7281, .adv_w = 182, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7358, .adv_w = 182, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7442, .adv_w = 208, .box_w = 14, .box_h = 14, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7540, .adv_w = 260, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7625, .adv_w = 156, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7695, .adv_w = 209, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xb12, 0x90f2, 0x90f9, 0x90fc, 0x90fd, 0x90fe, 0x9102,
    0x9104, 0x9106, 0x910a, 0x910d, 0x9112, 0x9117, 0x9118, 0x9119,
    0x912f, 0x9134, 0x9139, 0x913c, 0x913d, 0x913e, 0x9142, 0x9143,
    0x9144, 0x9145, 0x9158, 0x9159, 0x915f, 0x9161, 0x9162, 0x9165,
    0x9168, 0x9169, 0x916a, 0x916c, 0x9184, 0x9186, 0x91b5, 0x91b6,
    0x91b8, 0x91ba, 0x91d1, 0x91d8, 0x91db, 0x91e4, 0x920d, 0x9215,
    0x924c, 0x92dc, 0x9331, 0x9332, 0x9333, 0x9334, 0x9335, 0x9378,
    0x9384, 0x93de, 0x93f5, 0x964b, 0x98b3, 0x9993
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 24335, .range_length = 39316, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 4, 4,
    2, 0, 3, 0, 0, 15, 0, 0,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 4, 4, -9,
    -30, -19, 5, -8, 0, -25, -2, 4,
    0, 0, 0, 0, 0, 0, -16, 0,
    -15, -5, 0, -10, -12, -1, -10, -9,
    -11, -9, -11, 0, 0, 0, -7, -21,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, -9, -7,
    0, 0, 0, -7, 0, -6, 0, -8,
    -4, -7, -12, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, -24, 0, -13, 5, 0, -14,
    -7, 0, 0, 0, -17, -3, -19, -14,
    0, -23, 4, 0, 0, -2, 0, 0,
    0, 0, 0, 0, -9, 0, -9, 0,
    0, -3, 0, 0, 0, -3, 0, 0,
    0, 3, 0, -7, 0, -9, -3, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, -6, 5, 0, 7, -3, 0,
    0, 0, 1, 0, -1, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, -5, 0, 0, 0,
    0, 0, 2, 0, 2, -2, 0, 2,
    0, 0, 0, -2, 0, 0, -3, 0,
    -2, 0, -2, -3, 0, 0, -2, -2,
    -2, -4, -2, -4, 0, -2, 5, 0,
    1, -27, -12, 9, -1, 0, -29, 0,
    5, 0, 0, 0, 0, 0, 0, -8,
    0, -6, -2, 0, -4, 0, -3, 0,
    -5, -7, -5, -5, 0, 0, 0, 0,
    4, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -6, -3,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, 0, -23, 4, 0, -1,
    -12, -3, 0, -3, 0, -5, 0, 0,
    0, 0, 0, -6, 0, -7, -9, 0,
    -3, -3, -9, -9, -14, -7, -14, 0,
    -10, -21, 0, -19, 5, 0, -15, -10,
    0, 4, -2, -27, -9, -31, -23, 0,
    -37, 0, -1, 0, -4, -4, 0, 0,
    0, -6, -5, -20, 0, -20, 0, -2,
    2, 0, 2, -30, -17, 3, 0, 0,
    -33, 0, 0, 0, -1, -1, -5, 0,
    -6, -7, 0, -6, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, -2,
    0, -2, 8, 0, -1, -2, 0, 0,
    1, -3, -3, -6, -4, 0, -10, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 5,
    0, 0, -3, 0, 0, -5, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 6, 0, -17,
    -24, -17, 7, -6, 0, -29, 0, 5,
    0, 4, 4, 0, 0, 0, -25, 0,
    -23, -9, 0, -19, -23, -7, -18, -22,
    -22, -22, -18, -2, 4, 0, -5, -16,
    -14, 0, -4, 0, -15, 0, 4, 0,
    0, 0, 0, 0, 0, -16, 0, -13,
    -3, 0, -9, -10, 0, -7, -5, -7,
    -5, -7, 0, 0, 4, -19, 2, 0,
    3, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, -7, 0,
    0, -4, -4, -6, -6, -13, 0, -13,
    0, -6, 3, 4, -15, -29, -23, 2,
    -12, 0, -28, -5, 0, 0, 0, 0,
    0, 0, 0, -24, 0, -22, -11, 0,
    -18, -20, -7, -16, -15, -15, -15, -16,
    0, 0, 2, -10, 4, 0, 2, -6,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, -7, 0, -7, 0, 0,
    -9, 0, 0, 0, 0, -6, 0, 0,
    0, 0, -18, 0, -15, -14, -2, -21,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -3, 0, 0, -10,
    0, -2, -7, 0, -9, 0, 0, 0,
    0, -23, 0, -15, -13, -7, -22, 0,
    -2, 0, 0, -2, 0, 0, 0, -1,
    0, -4, -5, -4, -5, 0, 1, 0,
    4, 5, 0, -2, 0, 0, 0, 0,
    -16, 0, -11, -7, 4, -16, 0, 0,
    0, -1, 3, 0, 0, 0, 5, 0,
    0, 1, 0, 3, 0, 0, 3, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 3, -1,
    0, -5, 0, 0, 0, 0, -15, 0,
    -14, -10, -3, -19, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, -1,
    0, 0, 0, 10, 0, -1, -16, 0,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, -5, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, -19, 0, -10, -9,
    0, -18, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, -6, 5, 0, -10, 0, 0,
    0, 0, -19, 0, -13, -12, 0, -18,
    0, -6, 0, -5, 0, 0, 0, -2,
    0, -2, 0, 0, 0, 0, 0, 5,
    0, 2, -22, -10, -6, 0, 0, -25,
    0, 0, 0, -9, 0, -11, -16, 0,
    -9, 0, -7, 0, 0, 0, -1, 6,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    -21, 0, -15, -10, -1, -22, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    -2, 0, -2, 2, 0, -1, 2, 0,
    6, 0, -5, 0, 0, 0, 0, -14,
    0, -10, 0, 0, -14, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -19,
    -9, -6, 0, 0, -17, 0, -22, 0,
    -9, -5, -13, -15, 0, -4, 0, -4,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 2, 0,
    -8, 0, 0, 0, 0, -22, 0, -11,
    -7, 0, -15, 0, -3, 0, -5, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, -7,
    0, 0, 0, 0, -20, 0, -12, -6,
    0, -15, 0, -3, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_13 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_13 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 13,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_13*/

