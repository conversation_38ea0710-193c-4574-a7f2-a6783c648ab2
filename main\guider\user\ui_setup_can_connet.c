#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

#define TAG "ui_setup_can_connet"

void events_init_Can_Connet_page (lv_ui *ui)
{
    lv_obj_add_event_cb(ui->Can_Connet_page_back, back_to_menu_event_handler, LV_EVENT_ALL, ui);
}

/**
 * @brief 更新CAN数据显示
 */
void ui_scr_can_connect_data_update(ui_notif_msg_t *msg)
{
    if (!msg || !msg->user_data) {
        return;
    }

    can_display_data_t *can_data = (can_display_data_t *)msg->user_data;
    
    if (!can_data->valid) {
        return;
    }
    lv_ui *ui = &guider_ui;
    // 更新电压显示 (小数点左移一位)
    if (ui->Can_Connet_page_voltage) {
        lv_label_set_text_fmt(ui->Can_Connet_page_voltage, "%ld.%dV",
                              can_data->voltage / 10, abs((int)(can_data->voltage % 10)));
    }

    // 更新电流显示
    if (ui->Can_Connet_page_current) {
        lv_label_set_text_fmt(ui->Can_Connet_page_current, "%dmA", abs(can_data->current));
    }

    // 更新温度显示 (小数点左移一位)
    if (ui->Can_Connet_page_temper) {
        lv_label_set_text_fmt(ui->Can_Connet_page_temper, "%ld.%d°C",
                              can_data->temperature / 10, abs((int)(can_data->temperature % 10)));
    }

    // 更新速度显示
    if (ui->Can_Connet_page_Speed) {
        lv_label_set_text_fmt(ui->Can_Connet_page_Speed, "%ldrad", can_data->speed);
    }

    // 更新位置显示
    if (ui->Can_Connet_page_Position) {
        lv_label_set_text_fmt(ui->Can_Connet_page_Position, "%ld°", can_data->position);
    }

    // 更新速度仪表盘指针
    if (ui->Can_Connet_page_meter_speed && ui->Can_Connet_page_meter_speed_scale_0_ndline_0) {
        // 将速度值限制在仪表盘范围内 (0-5000)
        int32_t meter_value = can_data->speed;
        if (meter_value < 0) meter_value = 0;
        if (meter_value > 5000) meter_value = 5000;

        lv_meter_set_indicator_value(ui->Can_Connet_page_meter_speed,
                                   ui->Can_Connet_page_meter_speed_scale_0_ndline_0,
                                   meter_value);
    }
}

/**
 * @brief 更新CAN状态显示
 */
void ui_scr_can_connect_status_update(ui_notif_msg_t *msg)
{
    if (!msg || !msg->user_data) {
        return;
    }
    
    can_status_data_t *can_status = (can_status_data_t *)msg->user_data;
    lv_ui *ui = &guider_ui;
    
    // 更新连接状态，可以修改标题栏颜色或显示文本
    if (ui->Can_Connet_page_label_state_true) {
        if (can_status->connected) {
            lv_label_set_text(ui->Can_Connet_page_label_state_true, "CAN connected");
        } else {
            lv_label_set_text(ui->Can_Connet_page_label_state_true, "CAN disconnected");
        }
    }
    
}