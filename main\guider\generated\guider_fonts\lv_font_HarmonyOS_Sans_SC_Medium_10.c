/*******************************************************************************
 * Size: 10 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_10
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_10 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_10

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x4b, 0x4b, 0x3a, 0x3a, 0x39, 0x2, 0x4b,

    /* U+0022 "\"" */
    0x33, 0x51, 0x67, 0xa3, 0x66, 0xa2,

    /* U+0023 "#" */
    0x0, 0xa5, 0x2c, 0x0, 0xd, 0x16, 0x90, 0x2e,
    0xfe, 0xfe, 0x60, 0x68, 0xd, 0x0, 0xcf, 0xee,
    0xfb, 0x0, 0xe0, 0x87, 0x0, 0x2c, 0xb, 0x30,
    0x0,

    /* U+0024 "$" */
    0x0, 0x33, 0x0, 0x0, 0x99, 0x0, 0x1e, 0xde,
    0xd1, 0x79, 0x77, 0x61, 0x3e, 0xc7, 0x0, 0x2,
    0xbf, 0xa0, 0x31, 0x77, 0xb6, 0x7c, 0xa9, 0xd4,
    0x6, 0xdd, 0x60, 0x0, 0x77, 0x0,

    /* U+0025 "%" */
    0x5d, 0xc2, 0xa, 0x60, 0xb3, 0x77, 0x4c, 0x0,
    0x4c, 0xb2, 0xd2, 0x0, 0x0, 0x8, 0x80, 0x0,
    0x0, 0x3d, 0x2b, 0xc4, 0x0, 0xc4, 0x77, 0x3b,
    0x6, 0xa0, 0x2c, 0xc5,

    /* U+0026 "&" */
    0x2, 0xbd, 0x90, 0x0, 0x9, 0x90, 0xe3, 0x0,
    0x4, 0xda, 0xa0, 0x0, 0xb, 0xcd, 0x18, 0x70,
    0x7a, 0x6, 0xdd, 0x20, 0x7a, 0x0, 0xbe, 0x10,
    0xa, 0xcc, 0x87, 0xc1,

    /* U+0027 "'" */
    0x33, 0x67, 0x66,

    /* U+0028 "(" */
    0x0, 0x91, 0x8, 0x80, 0xe, 0x10, 0x3d, 0x0,
    0x5b, 0x0, 0x4c, 0x0, 0x1f, 0x0, 0xa, 0x60,
    0x1, 0xd1,

    /* U+0029 ")" */
    0x65, 0x0, 0xe1, 0x8, 0x80, 0x4c, 0x2, 0xe0,
    0x3d, 0x7, 0x90, 0xd3, 0x79, 0x0,

    /* U+002A "*" */
    0x23, 0x93, 0x4, 0xce, 0x80, 0x6a, 0xca, 0x0,
    0x15, 0x0,

    /* U+002B "+" */
    0x0, 0x43, 0x0, 0x0, 0x95, 0x0, 0x7e, 0xfe,
    0xe4, 0x0, 0x95, 0x0, 0x0, 0x95, 0x0,

    /* U+002C "," */
    0x1, 0x3, 0xe0, 0x1a, 0x2, 0x10,

    /* U+002D "-" */
    0x5d, 0xdd, 0x30,

    /* U+002E "." */
    0x1, 0x4b,

    /* U+002F "/" */
    0x0, 0x5b, 0x0, 0xb4, 0x1, 0xe0, 0x7, 0x80,
    0xd, 0x20, 0x4b, 0x0, 0xa5, 0x0,

    /* U+0030 "0" */
    0x7, 0xed, 0x40, 0x2e, 0x13, 0xe0, 0x7a, 0x0,
    0xe3, 0x89, 0x0, 0xd5, 0x7a, 0x0, 0xe3, 0x2e,
    0x13, 0xe0, 0x7, 0xed, 0x40,

    /* U+0031 "1" */
    0x2, 0xaf, 0xd, 0x8f, 0x0, 0x2f, 0x0, 0x2f,
    0x0, 0x2f, 0x0, 0x2f, 0x0, 0x2f,

    /* U+0032 "2" */
    0x9, 0xed, 0x50, 0x4b, 0x4, 0xf0, 0x0, 0x3,
    0xf0, 0x0, 0xc, 0x70, 0x0, 0xba, 0x0, 0xa,
    0xa0, 0x0, 0x7f, 0xee, 0xe4,

    /* U+0033 "3" */
    0x9, 0xed, 0x40, 0x4a, 0x4, 0xf0, 0x0, 0x6,
    0xd0, 0x0, 0xdf, 0x60, 0x0, 0x2, 0xf2, 0x49,
    0x1, 0xf2, 0xa, 0xed, 0x70,

    /* U+0034 "4" */
    0x0, 0x3e, 0x0, 0x0, 0xb6, 0x0, 0x3, 0xe1,
    0x10, 0xc, 0x67, 0xa0, 0x4d, 0x7, 0xa0, 0x9e,
    0xee, 0xf9, 0x0, 0x7, 0xa0,

    /* U+0035 "5" */
    0xc, 0xee, 0xc0, 0xe, 0x20, 0x0, 0xf, 0xdd,
    0x50, 0x8, 0x4, 0xf1, 0x0, 0x0, 0xe3, 0x1b,
    0x3, 0xf1, 0x9, 0xed, 0x40,

    /* U+0036 "6" */
    0x0, 0x3e, 0x10, 0x0, 0xd7, 0x0, 0x8, 0xd0,
    0x0, 0x2f, 0xed, 0x70, 0x6d, 0x1, 0xe4, 0x4d,
    0x1, 0xe4, 0x8, 0xee, 0x70,

    /* U+0037 "7" */
    0x6e, 0xee, 0xf3, 0x0, 0x4, 0xd0, 0x0, 0xb,
    0x70, 0x0, 0x2f, 0x10, 0x0, 0x89, 0x0, 0x0,
    0xe3, 0x0, 0x6, 0xc0, 0x0,

    /* U+0038 "8" */
    0x7, 0xed, 0x50, 0x3e, 0x2, 0xf0, 0x2e, 0x13,
    0xe0, 0xa, 0xff, 0x70, 0x6c, 0x1, 0xe2, 0x7c,
    0x1, 0xe4, 0xa, 0xed, 0x80,

    /* U+0039 "9" */
    0x9, 0xee, 0x50, 0x7b, 0x1, 0xf2, 0x7c, 0x1,
    0xf3, 0x9, 0xdf, 0xe0, 0x0, 0x1f, 0x50, 0x0,
    0xab, 0x0, 0x3, 0xe1, 0x0,

    /* U+003A ":" */
    0x3c, 0x2, 0x0, 0x2, 0x3c,

    /* U+003B ";" */
    0x1d, 0x0, 0x20, 0x0, 0x0, 0x20, 0x2f, 0x0,
    0xa0, 0x21, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x63, 0x3, 0x9d, 0x81, 0x7f, 0x80,
    0x0, 0x6, 0xcc, 0x50, 0x0, 0x2, 0x94,

    /* U+003D "=" */
    0x7d, 0xdd, 0xd4, 0x0, 0x0, 0x0, 0x6d, 0xdd,
    0xd4,

    /* U+003E ">" */
    0x44, 0x0, 0x0, 0x2a, 0xd8, 0x20, 0x0, 0x1a,
    0xf4, 0x17, 0xdb, 0x50, 0x67, 0x10, 0x0,

    /* U+003F "?" */
    0x5, 0xde, 0xa0, 0xa, 0x20, 0xd5, 0x0, 0x4,
    0xd1, 0x0, 0x3d, 0x10, 0x0, 0x67, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x5a, 0x0,

    /* U+0040 "@" */
    0x0, 0x29, 0xcc, 0xa3, 0x0, 0x5, 0xc3, 0x0,
    0x2b, 0x60, 0x1d, 0x6, 0xdb, 0xc2, 0xd1, 0x67,
    0x1e, 0x13, 0xf1, 0x85, 0x75, 0x4b, 0x0, 0xe1,
    0x76, 0x67, 0x1d, 0x2, 0xf3, 0x94, 0x1d, 0x5,
    0xca, 0x5c, 0x90, 0x5, 0xc3, 0x0, 0x31, 0x0,
    0x0, 0x3a, 0xcc, 0xa2, 0x0,

    /* U+0041 "A" */
    0x0, 0x4f, 0x10, 0x0, 0xb, 0xc7, 0x0, 0x1,
    0xd2, 0xd0, 0x0, 0x87, 0xc, 0x40, 0xe, 0xee,
    0xeb, 0x5, 0xa0, 0x0, 0xe2, 0xb5, 0x0, 0x9,
    0x80,

    /* U+0042 "B" */
    0x2f, 0xee, 0xc2, 0x2, 0xe0, 0x8, 0xb0, 0x2e,
    0x0, 0x8a, 0x2, 0xfd, 0xef, 0x50, 0x2e, 0x0,
    0x2f, 0x12, 0xe0, 0x2, 0xf2, 0x2f, 0xee, 0xd7,
    0x0,

    /* U+0043 "C" */
    0x2, 0xbe, 0xe8, 0x0, 0xe6, 0x1, 0xb2, 0x6c,
    0x0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x5c, 0x0,
    0x0, 0x0, 0xd7, 0x1, 0xb2, 0x2, 0xbe, 0xe8,
    0x0,

    /* U+0044 "D" */
    0x2f, 0xee, 0xb3, 0x2, 0xe0, 0x5, 0xe2, 0x2e,
    0x0, 0x9, 0x92, 0xe0, 0x0, 0x6b, 0x2e, 0x0,
    0x9, 0x92, 0xe0, 0x5, 0xf2, 0x2f, 0xee, 0xb3,
    0x0,

    /* U+0045 "E" */
    0x2f, 0xee, 0xe6, 0x2e, 0x0, 0x0, 0x2e, 0x0,
    0x0, 0x2f, 0xee, 0xe0, 0x2e, 0x0, 0x0, 0x2e,
    0x0, 0x0, 0x2f, 0xee, 0xe7,

    /* U+0046 "F" */
    0x2f, 0xee, 0xe6, 0x2e, 0x0, 0x0, 0x2e, 0x0,
    0x0, 0x2f, 0xee, 0xe0, 0x2e, 0x0, 0x0, 0x2e,
    0x0, 0x0, 0x2e, 0x0, 0x0,

    /* U+0047 "G" */
    0x1, 0xae, 0xea, 0x10, 0xd7, 0x0, 0x83, 0x6c,
    0x0, 0x0, 0x8, 0x90, 0xd, 0xe9, 0x5c, 0x0,
    0x7, 0xa0, 0xd8, 0x0, 0x99, 0x1, 0xae, 0xeb,
    0x20,

    /* U+0048 "H" */
    0x2e, 0x0, 0x5, 0xb2, 0xe0, 0x0, 0x5b, 0x2e,
    0x0, 0x5, 0xb2, 0xfe, 0xee, 0xfb, 0x2e, 0x0,
    0x5, 0xb2, 0xe0, 0x0, 0x5b, 0x2e, 0x0, 0x5,
    0xb0,

    /* U+0049 "I" */
    0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e,

    /* U+004A "J" */
    0x0, 0xf, 0x0, 0x0, 0xf0, 0x0, 0xf, 0x0,
    0x0, 0xf0, 0x0, 0xf, 0x8, 0x14, 0xe0, 0x7e,
    0xe4, 0x0,

    /* U+004B "K" */
    0x2e, 0x0, 0x3e, 0x32, 0xe0, 0x3e, 0x30, 0x2e,
    0x2e, 0x40, 0x2, 0xfd, 0xf3, 0x0, 0x2f, 0x46,
    0xe1, 0x2, 0xe0, 0x9, 0xc0, 0x2e, 0x0, 0xc,
    0x80,

    /* U+004C "L" */
    0x2e, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x2e, 0x0,
    0x0, 0x2e, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x2e,
    0x0, 0x0, 0x2f, 0xee, 0xe6,

    /* U+004D "M" */
    0x2f, 0x10, 0x0, 0x5d, 0x2f, 0xb0, 0x1, 0xed,
    0x2d, 0xb6, 0xa, 0x9d, 0x2d, 0x1e, 0x6c, 0x3d,
    0x2d, 0x6, 0xf2, 0x3d, 0x2d, 0x0, 0x10, 0x3d,
    0x2d, 0x0, 0x0, 0x3d,

    /* U+004E "N" */
    0x2f, 0x20, 0x6, 0xa2, 0xfd, 0x0, 0x6a, 0x2d,
    0x99, 0x6, 0xa2, 0xd0, 0xd5, 0x6a, 0x2d, 0x2,
    0xe8, 0xa2, 0xd0, 0x6, 0xfa, 0x2d, 0x0, 0xa,
    0xa0,

    /* U+004F "O" */
    0x2, 0xbe, 0xe9, 0x0, 0x1e, 0x60, 0x1b, 0xa0,
    0x6c, 0x0, 0x1, 0xf1, 0x8a, 0x0, 0x0, 0xf2,
    0x6c, 0x0, 0x1, 0xf1, 0x1e, 0x70, 0x1b, 0x90,
    0x2, 0xbe, 0xe8, 0x0,

    /* U+0050 "P" */
    0x2f, 0xee, 0xa1, 0x2e, 0x0, 0xaa, 0x2e, 0x0,
    0x5d, 0x2e, 0x0, 0xa9, 0x2f, 0xee, 0xa0, 0x2e,
    0x0, 0x0, 0x2e, 0x0, 0x0,

    /* U+0051 "Q" */
    0x2, 0xbe, 0xe9, 0x0, 0x1e, 0x60, 0x1b, 0xa0,
    0x6c, 0x0, 0x1, 0xf1, 0x8a, 0x0, 0x0, 0xf2,
    0x6c, 0x0, 0x1, 0xf1, 0x1e, 0x70, 0x1b, 0xa0,
    0x2, 0xbe, 0xfd, 0x0, 0x0, 0x0, 0x2d, 0x90,
    0x0, 0x0, 0x0, 0x74,

    /* U+0052 "R" */
    0x2f, 0xed, 0xb2, 0x2, 0xe0, 0x8, 0xb0, 0x2e,
    0x0, 0x8b, 0x2, 0xfe, 0xfc, 0x10, 0x2e, 0x6,
    0xd0, 0x2, 0xe0, 0xb, 0x80, 0x2e, 0x0, 0x1e,
    0x30,

    /* U+0053 "S" */
    0x9, 0xee, 0x90, 0x7b, 0x0, 0xa3, 0x6e, 0x30,
    0x0, 0x5, 0xbe, 0x80, 0x0, 0x1, 0xd7, 0xa8,
    0x0, 0xc7, 0x1a, 0xee, 0xa0,

    /* U+0054 "T" */
    0xce, 0xff, 0xea, 0x0, 0xa7, 0x0, 0x0, 0xa7,
    0x0, 0x0, 0xa7, 0x0, 0x0, 0xa7, 0x0, 0x0,
    0xa7, 0x0, 0x0, 0xa7, 0x0,

    /* U+0055 "U" */
    0x3d, 0x0, 0x7, 0xa3, 0xd0, 0x0, 0x7a, 0x3d,
    0x0, 0x7, 0xa3, 0xd0, 0x0, 0x7a, 0x3e, 0x0,
    0x8, 0x90, 0xe6, 0x2, 0xe4, 0x3, 0xcf, 0xe7,
    0x0,

    /* U+0056 "V" */
    0xb7, 0x0, 0x9, 0x85, 0xd0, 0x0, 0xe1, 0xe,
    0x40, 0x5b, 0x0, 0x7a, 0xb, 0x40, 0x1, 0xf3,
    0xe0, 0x0, 0xa, 0xd8, 0x0, 0x0, 0x3f, 0x10,
    0x0,

    /* U+0057 "W" */
    0xc5, 0x0, 0xa8, 0x0, 0x78, 0x6a, 0x0, 0xed,
    0x0, 0xc3, 0x1f, 0x4, 0xbd, 0x31, 0xe0, 0xb,
    0x58, 0x68, 0x85, 0x90, 0x6, 0xad, 0x13, 0xda,
    0x40, 0x1, 0xfb, 0x0, 0xde, 0x0, 0x0, 0xb6,
    0x0, 0x8a, 0x0,

    /* U+0058 "X" */
    0x7d, 0x0, 0x1e, 0x30, 0xb9, 0xb, 0x70, 0x1,
    0xeb, 0xb0, 0x0, 0x6, 0xf3, 0x0, 0x1, 0xea,
    0xc0, 0x0, 0xc7, 0xc, 0x80, 0x8b, 0x0, 0x1e,
    0x40,

    /* U+0059 "Y" */
    0x9a, 0x0, 0x3e, 0x11, 0xe4, 0xc, 0x50, 0x5,
    0xe6, 0xc0, 0x0, 0xa, 0xf2, 0x0, 0x0, 0x4c,
    0x0, 0x0, 0x4, 0xc0, 0x0, 0x0, 0x4c, 0x0,
    0x0,

    /* U+005A "Z" */
    0x6e, 0xee, 0xf5, 0x0, 0x6, 0xc0, 0x0, 0x2e,
    0x20, 0x0, 0xc7, 0x0, 0x7, 0xc0, 0x0, 0x2e,
    0x20, 0x0, 0xbf, 0xee, 0xe8,

    /* U+005B "[" */
    0x2e, 0xe5, 0x2d, 0x0, 0x2d, 0x0, 0x2d, 0x0,
    0x2d, 0x0, 0x2d, 0x0, 0x2d, 0x0, 0x2d, 0x0,
    0x2e, 0xe5,

    /* U+005C "\\" */
    0xa5, 0x0, 0x4b, 0x0, 0xd, 0x20, 0x7, 0x80,
    0x1, 0xe0, 0x0, 0xb4, 0x0, 0x5b,

    /* U+005D "]" */
    0xbe, 0xa0, 0x5b, 0x5, 0xb0, 0x5b, 0x5, 0xb0,
    0x5b, 0x5, 0xb0, 0x5b, 0xbe, 0xa0,

    /* U+005E "^" */
    0x1, 0x70, 0x0, 0x8e, 0x60, 0xe, 0x3d, 0x7,
    0x80, 0x96,

    /* U+005F "_" */
    0xdd, 0xdd, 0x40,

    /* U+0060 "`" */
    0x16, 0x0, 0xa4,

    /* U+0061 "a" */
    0xa, 0xed, 0x31, 0x60, 0x5c, 0x19, 0xbc, 0xe7,
    0xa0, 0x3e, 0x2c, 0xaa, 0xe0,

    /* U+0062 "b" */
    0x4c, 0x0, 0x0, 0x4c, 0x0, 0x0, 0x4d, 0xbe,
    0xa0, 0x4f, 0x20, 0xa7, 0x4c, 0x0, 0x6a, 0x4f,
    0x20, 0xa7, 0x4b, 0xbe, 0xa0,

    /* U+0063 "c" */
    0x8, 0xee, 0x55, 0xc0, 0x13, 0x88, 0x0, 0x5,
    0xc0, 0x13, 0x7, 0xed, 0x60,

    /* U+0064 "d" */
    0x0, 0x0, 0x96, 0x0, 0x0, 0x96, 0x9, 0xec,
    0xb6, 0x5c, 0x1, 0xe6, 0x88, 0x0, 0xa6, 0x5b,
    0x0, 0xd6, 0x9, 0xdb, 0xb6,

    /* U+0065 "e" */
    0x8, 0xdd, 0x50, 0x5c, 0x1, 0xf1, 0x8d, 0xbb,
    0xd2, 0x5c, 0x0, 0x40, 0x8, 0xee, 0x80,

    /* U+0066 "f" */
    0x6, 0xda, 0xe, 0x20, 0xbf, 0xd5, 0xf, 0x10,
    0xf, 0x10, 0xf, 0x10, 0xf, 0x10,

    /* U+0067 "g" */
    0x9, 0xec, 0xa6, 0x5c, 0x1, 0xe6, 0x88, 0x0,
    0xa6, 0x5c, 0x1, 0xd6, 0x8, 0xec, 0xc6, 0x15,
    0x0, 0xd3, 0xa, 0xed, 0x70,

    /* U+0068 "h" */
    0x4c, 0x0, 0x0, 0x4c, 0x0, 0x0, 0x4d, 0xbe,
    0x90, 0x4f, 0x11, 0xf2, 0x4c, 0x0, 0xc3, 0x4c,
    0x0, 0xc3, 0x4c, 0x0, 0xc3,

    /* U+0069 "i" */
    0x4b, 0x1, 0x4c, 0x4c, 0x4c, 0x4c, 0x4c,

    /* U+006A "j" */
    0x0, 0x4b, 0x0, 0x1, 0x0, 0x4c, 0x0, 0x4c,
    0x0, 0x4c, 0x0, 0x4c, 0x0, 0x4c, 0x0, 0x6b,
    0x1d, 0xd3,

    /* U+006B "k" */
    0x4c, 0x0, 0x0, 0x4c, 0x0, 0x0, 0x4c, 0x7,
    0xb0, 0x4c, 0x5c, 0x0, 0x4e, 0xfa, 0x0, 0x4e,
    0x2d, 0x50, 0x4c, 0x3, 0xe2,

    /* U+006C "l" */
    0x4c, 0x4c, 0x4c, 0x4c, 0x4c, 0x4c, 0x4c,

    /* U+006D "m" */
    0x4c, 0xbd, 0x6a, 0xd6, 0x4e, 0x4, 0xf1, 0x3e,
    0x4c, 0x2, 0xd0, 0xf, 0x4c, 0x2, 0xd0, 0xf,
    0x4c, 0x2, 0xd0, 0xf,

    /* U+006E "n" */
    0x4c, 0xbd, 0x90, 0x4e, 0x10, 0xe2, 0x4c, 0x0,
    0xc3, 0x4c, 0x0, 0xc3, 0x4c, 0x0, 0xc3,

    /* U+006F "o" */
    0x8, 0xee, 0x70, 0x5c, 0x1, 0xd4, 0x88, 0x0,
    0x97, 0x5c, 0x1, 0xd4, 0x8, 0xee, 0x70,

    /* U+0070 "p" */
    0x4c, 0xac, 0xa0, 0x4f, 0x10, 0x97, 0x4c, 0x0,
    0x6a, 0x4f, 0x20, 0xb7, 0x4d, 0xbe, 0xa0, 0x4c,
    0x0, 0x0, 0x4c, 0x0, 0x0,

    /* U+0071 "q" */
    0x9, 0xec, 0xa6, 0x5c, 0x1, 0xe6, 0x88, 0x0,
    0xa6, 0x5c, 0x1, 0xd6, 0x9, 0xec, 0xb6, 0x0,
    0x0, 0x96, 0x0, 0x0, 0x96,

    /* U+0072 "r" */
    0x4c, 0xb9, 0x4f, 0x10, 0x4c, 0x0, 0x4c, 0x0,
    0x4c, 0x0,

    /* U+0073 "s" */
    0x2c, 0xeb, 0x7, 0xa0, 0x40, 0x19, 0xd9, 0x4,
    0x20, 0xd4, 0x4d, 0xdb, 0x0,

    /* U+0074 "t" */
    0x7, 0x0, 0xf, 0x0, 0xcf, 0xd6, 0xf, 0x0,
    0xf, 0x0, 0xf, 0x10, 0x8, 0xe9,

    /* U+0075 "u" */
    0x5b, 0x0, 0xe2, 0x5b, 0x0, 0xe2, 0x5b, 0x0,
    0xe2, 0x4d, 0x1, 0xf2, 0xa, 0xda, 0xd2,

    /* U+0076 "v" */
    0xb6, 0x0, 0xe1, 0x4d, 0x6, 0x90, 0xd, 0x4c,
    0x20, 0x6, 0xcb, 0x0, 0x0, 0xe5, 0x0,

    /* U+0077 "w" */
    0xb5, 0x9, 0x90, 0x4a, 0x5a, 0xd, 0xe0, 0xa4,
    0xe, 0x4a, 0xa5, 0xd0, 0x9, 0xe4, 0x4e, 0x80,
    0x2, 0xe0, 0xe, 0x20,

    /* U+0078 "x" */
    0x8b, 0x8, 0xa0, 0xc, 0xad, 0x0, 0x3, 0xf5,
    0x0, 0xb, 0x9d, 0x0, 0x8a, 0x9, 0xa0,

    /* U+0079 "y" */
    0xb6, 0x0, 0xe1, 0x4d, 0x6, 0xa0, 0xc, 0x4c,
    0x30, 0x5, 0xdc, 0x0, 0x0, 0xe5, 0x0, 0x2,
    0xd0, 0x0, 0xbe, 0x40, 0x0,

    /* U+007A "z" */
    0x6c, 0xdf, 0x40, 0x8, 0xa0, 0x4, 0xd0, 0x1,
    0xe3, 0x0, 0x9f, 0xcc, 0x50,

    /* U+007B "{" */
    0x2, 0xc8, 0x8, 0x90, 0x8, 0x80, 0xb, 0x70,
    0x9e, 0x10, 0x1c, 0x60, 0x8, 0x80, 0x8, 0x90,
    0x2, 0xc8,

    /* U+007C "|" */
    0x55, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77,

    /* U+007D "}" */
    0xbb, 0x0, 0xc, 0x50, 0xb, 0x60, 0x9, 0x80,
    0x2, 0xf6, 0x9, 0xa1, 0xa, 0x60, 0xc, 0x50,
    0xbc, 0x10,

    /* U+007E "~" */
    0x1c, 0xb1, 0xb2, 0x58, 0x3d, 0xa0,

    /* U+00B0 "°" */
    0x5a, 0x70, 0xc0, 0xc0, 0x5b, 0x70,

    /* U+4E2D "中" */
    0x0, 0xb, 0x40, 0x0, 0xee, 0xef, 0xfe, 0xe6,
    0xf0, 0xb, 0x40, 0x77, 0xf0, 0xb, 0x40, 0x77,
    0xf0, 0xb, 0x40, 0x77, 0xef, 0xff, 0xff, 0xf7,
    0x0, 0xb, 0x40, 0x0, 0x0, 0xb, 0x40, 0x0,
    0x0, 0xb, 0x40, 0x0,

    /* U+4E8C "二" */
    0x0, 0x11, 0x11, 0x11, 0x0, 0xd, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xf3,

    /* U+4EF6 "件" */
    0x0, 0x84, 0x41, 0xe1, 0x0, 0x0, 0xe1, 0xd2,
    0xe3, 0x20, 0x6, 0xc2, 0xec, 0xfc, 0xc3, 0xe,
    0xc8, 0x60, 0xe1, 0x0, 0x6a, 0xc2, 0x10, 0xe1,
    0x0, 0x2, 0xc9, 0xee, 0xfe, 0xe9, 0x2, 0xc0,
    0x0, 0xe1, 0x0, 0x2, 0xc0, 0x0, 0xe1, 0x0,
    0x2, 0xb0, 0x0, 0xe1, 0x0,

    /* U+4F4D "位" */
    0x0, 0x32, 0x2, 0x50, 0x0, 0x0, 0xc4, 0x1,
    0xe0, 0x0, 0x3, 0xe7, 0xee, 0xfe, 0xe7, 0xa,
    0xc0, 0x50, 0x1, 0x50, 0x4e, 0xc0, 0xb3, 0x5,
    0xa0, 0x45, 0xc0, 0x68, 0xa, 0x50, 0x2, 0xc0,
    0x1d, 0xe, 0x0, 0x2, 0xc0, 0x7, 0x4a, 0x0,
    0x2, 0xca, 0xdd, 0xee, 0xd9, 0x2, 0xc1, 0x11,
    0x11, 0x11,

    /* U+4FE1 "信" */
    0x0, 0x50, 0x4, 0x30, 0x0, 0x4, 0xc5, 0x5a,
    0xc5, 0x53, 0xa, 0x78, 0x88, 0x88, 0x84, 0x2f,
    0x39, 0xcc, 0xcc, 0x90, 0xbf, 0x34, 0x55, 0x55,
    0x40, 0x5a, 0x35, 0x77, 0x77, 0x50, 0xa, 0x3a,
    0xdd, 0xdd, 0xb0, 0xa, 0x3c, 0x20, 0x1, 0xd0,
    0xa, 0x3c, 0xdd, 0xdd, 0xd0, 0xa, 0x3c, 0x10,
    0x0, 0xd0,

    /* U+51FB "击" */
    0x0, 0x0, 0x3b, 0x0, 0x0, 0x8, 0xee, 0xff,
    0xee, 0xe0, 0x0, 0x0, 0x3b, 0x0, 0x0, 0x2c,
    0xcc, 0xde, 0xcc, 0xc8, 0x2, 0x22, 0x5b, 0x22,
    0x21, 0x3, 0x50, 0x3b, 0x1, 0x80, 0x5, 0x90,
    0x3b, 0x1, 0xd0, 0x5, 0xed, 0xef, 0xdd, 0xd0,
    0x0, 0x11, 0x11, 0x12, 0xd0,

    /* U+5236 "制" */
    0x8, 0x2c, 0x0, 0x21, 0xc0, 0xed, 0xfd, 0xa8,
    0x4e, 0x25, 0x1c, 0x0, 0x84, 0xe6, 0xdd, 0xfd,
    0xd8, 0x4e, 0x2, 0x3d, 0x21, 0x84, 0xe0, 0xfb,
    0xfb, 0xb8, 0x4e, 0xd, 0x1c, 0x2b, 0x52, 0xe0,
    0xd1, 0xcc, 0x80, 0xe, 0x0, 0x1c, 0x0, 0x6e,
    0xc0,

    /* U+529B "力" */
    0x0, 0x1, 0xe0, 0x0, 0x0, 0xbb, 0xbf, 0xbb,
    0xba, 0x4, 0x46, 0xd4, 0x45, 0xe0, 0x0, 0x5a,
    0x0, 0x2d, 0x0, 0x8, 0x80, 0x2, 0xc0, 0x0,
    0xd3, 0x0, 0x4b, 0x0, 0x7c, 0x0, 0x5, 0xa0,
    0x6e, 0x20, 0x0, 0x99, 0x2d, 0x20, 0xc, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+52A0 "加" */
    0x1, 0xd0, 0x1, 0x44, 0x46, 0xef, 0xed, 0x4c,
    0x7f, 0x2, 0xc0, 0xe4, 0x90, 0xe0, 0x3b, 0xe,
    0x49, 0xe, 0x4, 0xa0, 0xe4, 0x90, 0xe0, 0x77,
    0x1d, 0x49, 0xe, 0xb, 0x42, 0xc4, 0x90, 0xe1,
    0xe0, 0x4b, 0x4f, 0xef, 0x69, 0x9f, 0x64, 0x90,
    0xe0, 0x0, 0x0, 0x0, 0x0,

    /* U+5347 "升" */
    0x3, 0x57, 0xa9, 0x3b, 0x0, 0x7, 0x6f, 0x10,
    0x3b, 0x0, 0x0, 0xf, 0x0, 0x3b, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0xf, 0x0, 0x3b,
    0x0, 0x0, 0x2d, 0x0, 0x3b, 0x0, 0x0, 0x89,
    0x0, 0x3b, 0x0, 0x5, 0xe1, 0x0, 0x3b, 0x0,
    0xc, 0x40, 0x0, 0x3b, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5370 "印" */
    0x0, 0x4, 0x40, 0x0, 0x0, 0xae, 0xc5, 0xce,
    0xee, 0xe, 0x10, 0xe, 0x21, 0xe0, 0xe0, 0x0,
    0xe1, 0xe, 0xe, 0xff, 0x9e, 0x10, 0xe0, 0xe0,
    0x0, 0xe1, 0xe, 0xe, 0x0, 0xe, 0x10, 0xe0,
    0xe6, 0xba, 0xe2, 0x4f, 0xe, 0xa5, 0x1e, 0x3a,
    0x70, 0x0, 0x0, 0xe1, 0x0,

    /* U+538B "压" */
    0xc, 0xee, 0xee, 0xee, 0xe2, 0xc, 0x30, 0xb,
    0x0, 0x0, 0xc, 0x20, 0xe, 0x10, 0x0, 0xc,
    0x7c, 0xcf, 0xdc, 0xa0, 0xd, 0x32, 0x2e, 0x45,
    0x10, 0xe, 0x0, 0xe, 0x3d, 0x30, 0x1d, 0x0,
    0xe, 0x13, 0x70, 0x89, 0xcd, 0xdf, 0xdd, 0xd4,
    0x41, 0x11, 0x11, 0x11, 0x10,

    /* U+56FA "固" */
    0xfd, 0xdd, 0xdd, 0xdf, 0xe0, 0x4, 0x50, 0xe,
    0xe8, 0xde, 0xed, 0x8e, 0xe0, 0x6, 0x70, 0xe,
    0xe0, 0xeb, 0xbf, 0xe, 0xe0, 0xd2, 0x2d, 0xe,
    0xe0, 0x78, 0x88, 0xe, 0xfb, 0xbb, 0xbb, 0xbf,
    0xe1, 0x11, 0x11, 0x1e,

    /* U+5907 "备" */
    0x0, 0x27, 0x0, 0x0, 0x0, 0x1, 0xde, 0xcc,
    0xca, 0x0, 0x2d, 0xc8, 0x4, 0xd5, 0x0, 0x55,
    0xc, 0xee, 0x20, 0x0, 0x7b, 0xd8, 0x48, 0xdc,
    0x90, 0x3a, 0xbb, 0xbb, 0xbb, 0x30, 0xb, 0x52,
    0xe2, 0x3d, 0x0, 0xb, 0xa8, 0xf8, 0x9d, 0x0,
    0xb, 0xba, 0xfa, 0xad, 0x0, 0xb, 0x52, 0x22,
    0x3d, 0x0,

    /* U+5BF9 "对" */
    0x25, 0x55, 0x0, 0xe, 0x10, 0x35, 0x5e, 0x6a,
    0xaf, 0xb2, 0x58, 0xd, 0x13, 0x3e, 0x40, 0xd,
    0x89, 0x65, 0xe, 0x10, 0x4, 0xf4, 0x1d, 0xe,
    0x10, 0x3, 0xf7, 0x8, 0x5e, 0x10, 0xc, 0x7e,
    0x20, 0xe, 0x10, 0x7c, 0x6, 0x40, 0xe, 0x0,
    0x81, 0x0, 0x8, 0xeb, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x34, 0x0, 0x0, 0x8, 0x99, 0xce,
    0x99, 0x92, 0xe, 0x4d, 0x54, 0x4e, 0x41, 0xe,
    0xcf, 0xcc, 0xcf, 0xc2, 0xe, 0xc, 0x54, 0x4d,
    0x0, 0xe, 0x5, 0x77, 0x76, 0x0, 0xe, 0x7e,
    0xcc, 0xce, 0x60, 0x3b, 0x8, 0xb3, 0xaa, 0x0,
    0x97, 0x37, 0xee, 0xe7, 0x30, 0x73, 0xb7, 0x20,
    0x27, 0xa2,

    /* U+6001 "态" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x97,
    0x11, 0x10, 0x3d, 0xdd, 0xff, 0xdd, 0xd3, 0x0,
    0x6, 0xcc, 0x70, 0x0, 0x1, 0x9f, 0x91, 0xca,
    0x20, 0x5e, 0x81, 0x93, 0x7, 0xe6, 0x14, 0x26,
    0x45, 0x4, 0x21, 0xd, 0x4b, 0xc, 0x26, 0xb0,
    0x6a, 0x2c, 0x0, 0x76, 0xa5, 0x21, 0xe, 0xdc,
    0xe3, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+606F "息" */
    0x0, 0x0, 0x22, 0x0, 0x0, 0x0, 0x16, 0x6c,
    0xb6, 0x65, 0x0, 0x2, 0xd5, 0x55, 0x56, 0xd0,
    0x0, 0x2e, 0xbb, 0xbb, 0xbd, 0x0, 0x2, 0xe9,
    0x99, 0x99, 0xd0, 0x0, 0x2e, 0xaa, 0xaa, 0xad,
    0x0, 0x2, 0x36, 0x49, 0x22, 0x40, 0x0, 0xb4,
    0xf0, 0xc5, 0x6d, 0x30, 0x1e, 0xf, 0x0, 0x1d,
    0x4b, 0x2, 0x60, 0xae, 0xdd, 0x70, 0x60,

    /* U+6253 "打" */
    0x1, 0xe0, 0x58, 0x88, 0x85, 0x6d, 0xfd, 0x11,
    0x2e, 0x10, 0x2, 0xe1, 0x0, 0x1e, 0x0, 0x1,
    0xe4, 0x0, 0x1e, 0x0, 0x4a, 0xfb, 0x10, 0x1e,
    0x0, 0x67, 0xe0, 0x0, 0x1e, 0x0, 0x1, 0xe0,
    0x0, 0x1e, 0x0, 0x1, 0xd0, 0x0, 0x1e, 0x0,
    0x4f, 0xa0, 0xb, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+63A7 "控" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x42, 0x46,
    0xd4, 0x40, 0x9e, 0xda, 0xa5, 0x55, 0xe0, 0x19,
    0x56, 0x6a, 0x26, 0x90, 0x9, 0x83, 0xd5, 0xb,
    0x90, 0x6e, 0xc7, 0x40, 0x0, 0x81, 0x6b, 0x42,
    0xee, 0xfe, 0x70, 0x9, 0x40, 0x1, 0xd0, 0x0,
    0x9, 0x40, 0x1, 0xd0, 0x0, 0x8e, 0x2c, 0xee,
    0xfe, 0xe2,

    /* U+641C "搜" */
    0xa, 0x22, 0x77, 0xc5, 0x40, 0x5d, 0x99, 0x83,
    0xb5, 0xd0, 0x4c, 0x79, 0xd8, 0xdb, 0xd0, 0xa,
    0x88, 0xa8, 0xd7, 0xd0, 0x7e, 0x92, 0x56, 0xc5,
    0x40, 0x5b, 0x37, 0xfc, 0xce, 0x80, 0xa, 0x30,
    0x89, 0x3d, 0x10, 0xa, 0x20, 0x3e, 0xf7, 0x0,
    0x9e, 0x4e, 0xb6, 0x4a, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+673A "机" */
    0x0, 0xe0, 0x1f, 0xff, 0x0, 0x4c, 0xfc, 0x3d,
    0xe, 0x0, 0x4, 0xe2, 0x2d, 0xe, 0x0, 0x8,
    0xf5, 0x1c, 0xe, 0x0, 0xd, 0xed, 0x5c, 0xe,
    0x0, 0x77, 0xe1, 0x3b, 0xe, 0x12, 0x40, 0xe0,
    0x68, 0xe, 0x39, 0x0, 0xe0, 0xb3, 0xe, 0x48,
    0x0, 0xe4, 0xa0, 0xb, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6D41 "流" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x3a, 0x0, 0xb,
    0x70, 0x0, 0x7, 0x8b, 0xdf, 0xdd, 0xd3, 0x52,
    0x0, 0xb6, 0x1b, 0x0, 0x5e, 0x4b, 0xd8, 0x9e,
    0xa0, 0x2, 0x8, 0x66, 0x34, 0x70, 0x6, 0x46,
    0x69, 0x3d, 0x0, 0xd, 0x27, 0x69, 0x3d, 0x3,
    0x2d, 0xc, 0x29, 0x3d, 0x29, 0x58, 0x98, 0x8,
    0x3b, 0xd6, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+6E29 "温" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0x2b, 0xdc,
    0xce, 0x50, 0x13, 0x6b, 0xbb, 0xbd, 0x50, 0xb8,
    0xb, 0x54, 0x4a, 0x50, 0xa, 0x26, 0x99, 0x99,
    0x20, 0x4, 0x1a, 0xaa, 0xaa, 0x60, 0xd, 0x2c,
    0x77, 0xd5, 0xa0, 0x1d, 0xc, 0x66, 0xc3, 0xa0,
    0x59, 0xc, 0x66, 0xc3, 0xa0, 0x85, 0xbf, 0xed,
    0xfd, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+70B9 "点" */
    0x0, 0x0, 0xf4, 0x44, 0x40, 0x0, 0x0, 0xf7,
    0x77, 0x70, 0x8, 0xcc, 0xfc, 0xcc, 0x0, 0xb,
    0x52, 0x22, 0x2e, 0x0, 0xb, 0x30, 0x0, 0xe,
    0x0, 0x9, 0xee, 0xee, 0xee, 0x0, 0x8, 0x6,
    0x6, 0x8, 0x20, 0x3c, 0xe, 0x19, 0x46, 0xb0,
    0x74, 0x8, 0x23, 0x30, 0x91,

    /* U+7259 "牙" */
    0xe, 0xee, 0xee, 0xfe, 0xe1, 0x1, 0x90, 0x0,
    0xc3, 0x0, 0x6, 0xa0, 0x0, 0xc3, 0x0, 0x8,
    0xfe, 0xee, 0xfe, 0xe4, 0x0, 0x0, 0xa8, 0xc3,
    0x0, 0x0, 0x8, 0xc0, 0xc3, 0x0, 0x1, 0xac,
    0x10, 0xc3, 0x0, 0x4e, 0x90, 0x0, 0xd2, 0x0,
    0x14, 0x0, 0x7e, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+72B6 "状" */
    0x0, 0xa3, 0x1, 0xd6, 0x40, 0x85, 0xb3, 0x1,
    0xd1, 0xb0, 0x1d, 0xb7, 0xff, 0xff, 0xf5, 0x6,
    0xc3, 0x2, 0xf0, 0x0, 0x3, 0xe3, 0x5, 0xf4,
    0x0, 0x5e, 0xd3, 0xb, 0x9a, 0x0, 0x92, 0xb3,
    0x4d, 0xd, 0x40, 0x0, 0xb5, 0xe5, 0x4, 0xe2,
    0x0, 0xba, 0x80, 0x0, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+73AF "环" */
    0x4d, 0xdc, 0xbf, 0xff, 0xf7, 0x3, 0xc1, 0x0,
    0xc4, 0x0, 0x2, 0xc0, 0x3, 0xf1, 0x0, 0x3d,
    0xfb, 0xc, 0xfd, 0x20, 0x2, 0xc0, 0x8a, 0xe5,
    0xd1, 0x2, 0xc2, 0xd0, 0xe1, 0x76, 0x3, 0xe8,
    0x0, 0xe1, 0x0, 0x7e, 0xb7, 0x0, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x0,

    /* U+7535 "电" */
    0x0, 0xd, 0x10, 0x0, 0xe, 0xee, 0xfe, 0xef,
    0x30, 0xe0, 0xd, 0x10, 0xc3, 0xe, 0xdd, 0xfd,
    0xdf, 0x30, 0xe0, 0xd, 0x10, 0xc3, 0xe, 0xdd,
    0xfd, 0xdf, 0x50, 0x0, 0xd, 0x10, 0xa, 0x40,
    0x0, 0xd2, 0x0, 0xc3, 0x0, 0x8, 0xed, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+77E9 "矩" */
    0x5, 0x10, 0x0, 0x0, 0x0, 0xe, 0x43, 0x8f,
    0xee, 0xe5, 0x4d, 0xf9, 0x87, 0x0, 0x0, 0x73,
    0xe0, 0x8e, 0xdd, 0xc0, 0x59, 0xf9, 0x97, 0x0,
    0xe0, 0x36, 0xf6, 0x97, 0x0, 0xe0, 0x2, 0xe0,
    0x8f, 0xee, 0xd0, 0x6, 0xd9, 0x87, 0x0, 0x0,
    0xd, 0x2d, 0xa7, 0x0, 0x0, 0x89, 0x2, 0x8f,
    0xee, 0xe6, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+7801 "码" */
    0x5d, 0xdd, 0x7e, 0xef, 0x60, 0x7, 0x81, 0x4,
    0x9, 0x50, 0xb, 0x40, 0x2b, 0xa, 0x30, 0x1f,
    0xb9, 0x49, 0xc, 0x20, 0x8f, 0x4d, 0x5e, 0xee,
    0xf3, 0x5c, 0x1d, 0x23, 0x32, 0xb2, 0xc, 0x1d,
    0x68, 0x85, 0xd0, 0xc, 0xed, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0xee, 0x90,

    /* U+7CFB "系" */
    0x0, 0x0, 0x0, 0x13, 0x50, 0x8, 0xdd, 0xee,
    0xca, 0x80, 0x0, 0x9, 0xa0, 0x4b, 0x0, 0x3,
    0xde, 0x9b, 0xd2, 0x0, 0x2, 0x65, 0xcb, 0x29,
    0x0, 0x1, 0x7e, 0xa5, 0x7e, 0x90, 0x7, 0xca,
    0xae, 0x64, 0xc3, 0x0, 0x58, 0x2c, 0x3a, 0x10,
    0x8, 0xd1, 0x2c, 0x6, 0xd3, 0x18, 0x6, 0xe9,
    0x0, 0x36,

    /* U+7D22 "索" */
    0x4, 0x55, 0x6d, 0x55, 0x40, 0x4, 0x55, 0x6d,
    0x55, 0x40, 0x1f, 0xcc, 0xfe, 0xcc, 0xf1, 0x1b,
    0x19, 0xa2, 0x93, 0xc1, 0x2, 0x9a, 0xec, 0x46,
    0x0, 0x8, 0xcf, 0xeb, 0xbd, 0x90, 0x2, 0x57,
    0x1e, 0x22, 0x40, 0x8, 0xd4, 0x1d, 0x3d, 0x80,
    0x49, 0x14, 0xd9, 0x0, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7EA7 "级" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x3, 0xb1, 0xef,
    0xee, 0x90, 0xb, 0x47, 0x49, 0x8, 0x60, 0x5d,
    0x88, 0x4b, 0xd, 0x20, 0x48, 0xe1, 0x5f, 0x1c,
    0xf2, 0x9, 0x61, 0x7e, 0x61, 0xc0, 0x4f, 0xc7,
    0xb5, 0xd9, 0x60, 0x0, 0x23, 0xe0, 0x9e, 0x0,
    0x6d, 0xcd, 0x88, 0xc9, 0xc1, 0x10, 0xa, 0x99,
    0x0, 0x68, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0xd0, 0x11,
    0xe2, 0x10, 0x6, 0x81, 0xcd, 0xec, 0xc6, 0x1d,
    0x5d, 0xc, 0x36, 0x20, 0x4b, 0xe7, 0x89, 0x39,
    0xc0, 0x2, 0xd2, 0xdf, 0xae, 0xa5, 0xc, 0xca,
    0xe, 0x1c, 0x0, 0x8, 0x41, 0x1c, 0x1c, 0x4,
    0x28, 0xce, 0x96, 0xc, 0xb, 0x36, 0x38, 0xb0,
    0xd, 0xe8, 0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+7EF4 "维" */
    0x1, 0x30, 0x14, 0x12, 0x0, 0x8, 0x70, 0x79,
    0x3b, 0x0, 0x1d, 0x47, 0xde, 0xdf, 0xd2, 0x9c,
    0xd8, 0xf1, 0x1c, 0x0, 0x49, 0x99, 0xed, 0xdf,
    0xd0, 0x1d, 0x52, 0xc1, 0x1c, 0x0, 0x7d, 0x91,
    0xcd, 0xdf, 0xd0, 0x2, 0x66, 0xc1, 0x1c, 0x0,
    0xbb, 0x72, 0xcd, 0xdf, 0xd4, 0x0, 0x0, 0xc1,
    0x0, 0x0,

    /* U+7F6E "置" */
    0x2e, 0xae, 0xac, 0xba, 0xb0, 0x2c, 0x9c, 0xab,
    0xa9, 0xa0, 0x4a, 0xaa, 0xdb, 0xaa, 0xa0, 0x3,
    0x88, 0xe9, 0x88, 0x0, 0x5, 0xc8, 0x88, 0x8e,
    0x0, 0x5, 0xb6, 0x66, 0x6e, 0x0, 0x5, 0xb5,
    0x55, 0x5e, 0x0, 0x5, 0xc8, 0x88, 0x8e, 0x0,
    0x8c, 0xdb, 0xbb, 0xbf, 0xb3,

    /* U+84DD "蓝" */
    0x14, 0x4e, 0x54, 0xc8, 0x43, 0x18, 0x8f, 0xa8,
    0xea, 0x85, 0x8, 0x6b, 0x54, 0xfa, 0xa5, 0x8,
    0x69, 0x6c, 0x6a, 0x31, 0x6, 0x48, 0x65, 0x7,
    0x90, 0x4, 0xcc, 0xcc, 0xcc, 0x90, 0x5, 0x93,
    0xa2, 0xa1, 0xc0, 0x5, 0x93, 0xa2, 0xa1, 0xc0,
    0x4d, 0xed, 0xed, 0xed, 0xfa,

    /* U+884C "行" */
    0x0, 0x13, 0x0, 0x0, 0x0, 0x0, 0xd6, 0x7e,
    0xee, 0xe3, 0x1c, 0x80, 0x0, 0x0, 0x0, 0x17,
    0x47, 0x0, 0x0, 0x0, 0x1, 0xe3, 0xde, 0xef,
    0xe9, 0x1d, 0xd0, 0x0, 0xe, 0x10, 0x68, 0xd0,
    0x0, 0xe, 0x10, 0x1, 0xd0, 0x0, 0xe, 0x10,
    0x1, 0xd0, 0x0, 0xf, 0x0, 0x1, 0xd0, 0xb,
    0xeb, 0x0,

    /* U+8BBE "设" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x7, 0xb0, 0xf,
    0xdf, 0x30, 0x0, 0x95, 0x1e, 0xb, 0x30, 0x1,
    0x11, 0xb8, 0xb, 0xb5, 0x4d, 0xd0, 0x81, 0x12,
    0x51, 0x1, 0xd0, 0xde, 0xcd, 0xc0, 0x1, 0xd0,
    0x1d, 0x9, 0x60, 0x1, 0xd7, 0x25, 0xca, 0x0,
    0x2, 0xfb, 0x4b, 0xbc, 0x50, 0x1, 0x75, 0xb4,
    0x2, 0xa6,

    /* U+8BDD "话" */
    0x2, 0x0, 0x0, 0x0, 0x20, 0x2e, 0x26, 0xcd,
    0xed, 0x90, 0x4, 0x71, 0x15, 0xa0, 0x0, 0x0,
    0x4, 0x48, 0xb4, 0x41, 0xbf, 0x39, 0xac, 0xea,
    0xa3, 0xb, 0x30, 0x5, 0xa0, 0x0, 0xb, 0x34,
    0xfd, 0xde, 0x90, 0xb, 0xa8, 0xa0, 0x5, 0x90,
    0xd, 0xb4, 0xfd, 0xde, 0x90, 0x4, 0x4, 0xa0,
    0x5, 0x90,

    /* U+8F68 "轨" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xa0, 0xd,
    0x10, 0x0, 0x7e, 0xed, 0x3d, 0x32, 0x0, 0xd,
    0x61, 0x9f, 0xcf, 0x0, 0x3a, 0xa2, 0xd, 0xd,
    0x0, 0x5d, 0xfd, 0x2d, 0xd, 0x0, 0x0, 0xb2,
    0xd, 0xd, 0x0, 0x59, 0xef, 0x9b, 0xd, 0x54,
    0x56, 0xc3, 0x95, 0xd, 0x65, 0x0, 0xb4, 0xa0,
    0xb, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8F7D "载" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x99, 0x47,
    0x79, 0x10, 0x15, 0xaa, 0x57, 0x83, 0x70, 0x9d,
    0xdd, 0xdd, 0xed, 0xd3, 0x25, 0xe5, 0x55, 0xb1,
    0x80, 0x4e, 0xa9, 0x84, 0xd8, 0x70, 0x2f, 0xce,
    0xb2, 0xed, 0x10, 0x2, 0x6c, 0x61, 0xd6, 0xa2,
    0x9e, 0xdd, 0x6a, 0xda, 0xc1, 0x0, 0x3a, 0x1a,
    0xc, 0xb0,

    /* U+8FD0 "运" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0x70, 0xcd,
    0xdd, 0xd0, 0x0, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xaa, 0xaa, 0xa6, 0x4e, 0xe2, 0x3c, 0x74,
    0x32, 0x0, 0xe0, 0x2d, 0x9, 0x50, 0x0, 0xe0,
    0xc7, 0x38, 0xe0, 0x0, 0xe3, 0xfd, 0xb8, 0xb6,
    0xb, 0xda, 0x10, 0x0, 0x10, 0x2a, 0x7, 0xde,
    0xed, 0xea, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FF9 "迹" */
    0x0, 0x0, 0x1, 0x30, 0x0, 0xb, 0x20, 0x2,
    0xd0, 0x0, 0x5, 0xd8, 0xdf, 0xdf, 0xd8, 0x0,
    0x30, 0x4d, 0x1c, 0x30, 0x5f, 0x94, 0x9d, 0x1c,
    0xc0, 0x3, 0xab, 0x3d, 0x1c, 0x76, 0x3, 0xa6,
    0x79, 0x1c, 0x19, 0x6, 0xd2, 0xe2, 0xd9, 0x0,
    0x1d, 0x99, 0x10, 0x0, 0x0, 0x46, 0x8, 0xed,
    0xdc, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+901A "通" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0x44, 0xcc,
    0xce, 0xd0, 0x2, 0xd0, 0x3c, 0xab, 0x20, 0x2,
    0x24, 0xd9, 0xe9, 0xe0, 0x3b, 0xd4, 0xeb, 0xfb,
    0xf0, 0x0, 0xd4, 0xa4, 0xe4, 0xd0, 0x0, 0xd4,
    0xb6, 0xe6, 0xe0, 0x3, 0xe4, 0x80, 0xb7, 0xd0,
    0xc, 0x7b, 0x10, 0x0, 0x0, 0x28, 0x5, 0xdd,
    0xdd, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+901F "速" */
    0x9, 0x21, 0x44, 0xf4, 0x41, 0x3, 0xd3, 0x77,
    0xf7, 0x72, 0x0, 0x2, 0xec, 0xfc, 0xe0, 0x4f,
    0xc2, 0xb0, 0xe0, 0xd0, 0x2, 0xc2, 0xcd, 0xfc,
    0xc0, 0x2, 0xc0, 0x2d, 0xfb, 0x20, 0x2, 0xd5,
    0xe3, 0xe4, 0xe2, 0xb, 0xda, 0x20, 0xa0, 0x10,
    0x29, 0x9, 0xdd, 0xdd, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0x10, 0x0, 0x16,
    0xbf, 0xff, 0xf2, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0x20, 0x5, 0xff, 0xd9, 0x41, 0xf2, 0x0, 0x5f,
    0x20, 0x0, 0x1f, 0x20, 0x5, 0xe0, 0x0, 0x1,
    0xf2, 0x0, 0x5e, 0x0, 0x7, 0x9f, 0x20, 0x48,
    0xe0, 0x7, 0xff, 0xf2, 0xaf, 0xfe, 0x0, 0x2b,
    0xd8, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x41, 0x88, 0x88, 0x88, 0x14, 0xeb, 0xe7, 0x77,
    0x7e, 0xbe, 0xa2, 0xd0, 0x0, 0xd, 0x2a, 0xeb,
    0xe3, 0x33, 0x3e, 0xbe, 0xb4, 0xfb, 0xbb, 0xbf,
    0x4b, 0xd9, 0xd0, 0x0, 0xd, 0x9d, 0xb5, 0xd0,
    0x0, 0xd, 0x5b, 0xb7, 0xff, 0xff, 0xff, 0x7b,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd6, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0xff, 0xff, 0xff, 0x67,
    0x52, 0x77, 0x77, 0x76, 0xef, 0xc6, 0xff, 0xff,
    0xfe, 0xff, 0xe7, 0xff, 0xff, 0xff, 0x67, 0x52,
    0x77, 0x77, 0x76, 0xef, 0xc6, 0xff, 0xff, 0xfe,
    0xff, 0xe7, 0xff, 0xff, 0xff, 0x78, 0x63, 0x88,
    0x88, 0x87,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x7c,
    0x10, 0x6, 0xff, 0x70, 0xdf, 0xc1, 0x6f, 0xf7,
    0x0, 0x1d, 0xfe, 0xff, 0x70, 0x0, 0x1, 0xdf,
    0xf7, 0x0, 0x0, 0x0, 0x1c, 0x60, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0xc, 0xd1, 0x2, 0xea, 0xaf,
    0xd4, 0xef, 0x80, 0xaf, 0xff, 0x80, 0x2, 0xff,
    0xf1, 0x2, 0xef, 0xdf, 0xd1, 0xdf, 0x80, 0xaf,
    0xb6, 0x70, 0x0, 0x85,

    /* U+F011 "" */
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x2, 0xa0, 0xea,
    0x29, 0x0, 0xe, 0xe1, 0xea, 0x5f, 0xa0, 0x7f,
    0x40, 0xea, 0x8, 0xf3, 0xbd, 0x0, 0xea, 0x1,
    0xf7, 0xcc, 0x0, 0xb7, 0x0, 0xf8, 0xaf, 0x0,
    0x0, 0x4, 0xf6, 0x4f, 0xa0, 0x0, 0x1d, 0xf1,
    0x9, 0xfd, 0x89, 0xef, 0x50, 0x0, 0x6d, 0xff,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x1, 0x88, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x1e, 0xcf, 0xff, 0xfc, 0xd0, 0x7f,
    0xff, 0xdd, 0xff, 0xf7, 0x2d, 0xfa, 0x0, 0xbf,
    0xd1, 0xb, 0xf7, 0x0, 0x8f, 0xa0, 0x6f, 0xfe,
    0x55, 0xef, 0xf6, 0x4f, 0xff, 0xff, 0xff, 0xf3,
    0x6, 0x3a, 0xff, 0xa3, 0x60, 0x0, 0x3, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x90, 0xf8, 0x0, 0x0, 0x9, 0xf8, 0xec,
    0xf8, 0x0, 0x1, 0xbe, 0x5a, 0x5c, 0xf8, 0x0,
    0x2d, 0xd5, 0xef, 0xf6, 0xaf, 0x50, 0xda, 0x6f,
    0xff, 0xff, 0x87, 0xf1, 0x11, 0xff, 0xff, 0xff,
    0xf5, 0x10, 0x2, 0xff, 0xc3, 0x9f, 0xf6, 0x0,
    0x2, 0xff, 0xb0, 0x7f, 0xf6, 0x0, 0x1, 0xbb,
    0x70, 0x4b, 0xb3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x88, 0x20, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x1, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x5f, 0xff, 0xfa, 0x0, 0x0, 0x5,
    0xff, 0xb0, 0x0, 0x8b, 0xb9, 0x8b, 0x8b, 0xb9,
    0xdf, 0xff, 0xff, 0xfe, 0xdf, 0xcf, 0xff, 0xff,
    0xfc, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x24, 0x44, 0x44, 0x30, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xf4, 0x0, 0xb, 0xc0, 0x0, 0x0,
    0x8e, 0x10, 0x6e, 0x10, 0x0, 0x0, 0xc, 0xa0,
    0xee, 0xcb, 0x10, 0xa, 0xcd, 0xf2, 0xff, 0xff,
    0xb8, 0x9f, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe1,

    /* U+F021 "" */
    0x0, 0x4, 0x87, 0x30, 0x5f, 0x2, 0xdf, 0xfe,
    0xfc, 0x7f, 0x1e, 0xd3, 0x0, 0x3c, 0xff, 0x9f,
    0x10, 0x5, 0xfe, 0xff, 0x44, 0x0, 0x2, 0x66,
    0x66, 0x12, 0x22, 0x0, 0x0, 0x11, 0xff, 0xff,
    0x50, 0x0, 0xda, 0xff, 0xa3, 0x10, 0x8, 0xf4,
    0xfc, 0xfb, 0x66, 0xbf, 0x80, 0xf5, 0x4c, 0xff,
    0xd5, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x70, 0x0, 0xbf, 0xab, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3e, 0xf0, 0x0, 0x2c,

    /* U+F027 "" */
    0x0, 0x0, 0x70, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0xab, 0xdf, 0xf0, 0x20, 0xff, 0xff, 0xf0, 0xa6,
    0xff, 0xff, 0xf0, 0x59, 0xff, 0xff, 0xf0, 0x92,
    0x0, 0x3e, 0xf0, 0x0, 0x0, 0x2, 0xc0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x70, 0x0, 0xaa, 0x0, 0x0, 0xb, 0xf0, 0xa,
    0x4a, 0x70, 0xab, 0xdf, 0xf0, 0x23, 0xe2, 0xe0,
    0xff, 0xff, 0xf0, 0xa6, 0x95, 0xc2, 0xff, 0xff,
    0xf0, 0x59, 0x76, 0xc3, 0xff, 0xff, 0xf0, 0x92,
    0xc3, 0xe1, 0x0, 0x3e, 0xf0, 0x9, 0xa6, 0xb0,
    0x0, 0x2, 0xc0, 0x3, 0x3e, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xc3, 0x0,

    /* U+F03E "" */
    0x24, 0x44, 0x44, 0x44, 0x42, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xf3, 0xd, 0xff, 0xef, 0xff, 0xf8,
    0x4e, 0xfe, 0x25, 0xff, 0xff, 0x9d, 0xe2, 0x0,
    0x6f, 0xf9, 0x1, 0x20, 0x0, 0x4f, 0xf7, 0x44,
    0x44, 0x44, 0x7f, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F043 "" */
    0x0, 0x1a, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x0,
    0xef, 0xc0, 0x0, 0x8f, 0xff, 0x60, 0x3f, 0xff,
    0xff, 0x1b, 0xff, 0xff, 0xf9, 0xfb, 0xff, 0xff,
    0xdd, 0x6e, 0xff, 0xfc, 0x7e, 0x59, 0xff, 0x60,
    0x9f, 0xff, 0x80, 0x0, 0x13, 0x10, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0xe, 0x70, 0x3, 0xe4, 0xe7,
    0x4, 0xff, 0x5e, 0x75, 0xff, 0xf5, 0xec, 0xff,
    0xff, 0x5e, 0xff, 0xff, 0xf5, 0xea, 0xef, 0xff,
    0x5e, 0x71, 0xdf, 0xf5, 0xe7, 0x1, 0xcf, 0x59,
    0x50, 0x0, 0x92,

    /* U+F04B "" */
    0x88, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x50, 0x0,
    0x0, 0xff, 0xff, 0xc3, 0x0, 0xf, 0xff, 0xff,
    0xf9, 0x10, 0xff, 0xff, 0xff, 0xfe, 0x5f, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xf8, 0xf,
    0xff, 0xff, 0xb2, 0x0, 0xff, 0xfd, 0x40, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x90, 0xdf,
    0xf9, 0xff, 0xfc, 0xf, 0xff, 0xcf, 0xff, 0xc0,
    0xff, 0xfc, 0xff, 0xfc, 0xf, 0xff, 0xcf, 0xff,
    0xc0, 0xff, 0xfc, 0xff, 0xfc, 0xf, 0xff, 0xcf,
    0xff, 0xc0, 0xff, 0xfc, 0xff, 0xfb, 0xf, 0xff,
    0xb8, 0xbb, 0x50, 0x8b, 0xb5,

    /* U+F04D "" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xb8, 0xbb, 0xbb, 0xbb, 0xb5,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0xb, 0xa0, 0x0, 0xe7, 0xcf,
    0xb0, 0xe, 0x7c, 0xff, 0xc1, 0xe7, 0xcf, 0xff,
    0xdf, 0x7c, 0xff, 0xff, 0xf7, 0xcf, 0xff, 0x9e,
    0x7c, 0xff, 0x70, 0xe7, 0xcf, 0x60, 0xe, 0x77,
    0x50, 0x0, 0x95,

    /* U+F052 "" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x60, 0x0, 0x0, 0x9, 0xff, 0xf5, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x40, 0x6, 0xff, 0xff, 0xff,
    0xf3, 0xe, 0xff, 0xff, 0xff, 0xfa, 0x3, 0x66,
    0x66, 0x66, 0x62, 0xd, 0xff, 0xff, 0xff, 0xf9,
    0xf, 0xff, 0xff, 0xff, 0xfb, 0x6, 0x88, 0x88,
    0x88, 0x84,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc7, 0x0, 0x1d,
    0xf5, 0x1, 0xdf, 0x50, 0x1d, 0xf5, 0x0, 0x4f,
    0xd0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x6f, 0xc0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x51,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x3e, 0x30, 0x0, 0x2e, 0xf3,
    0x0, 0x2, 0xef, 0x30, 0x0, 0x2e, 0xe3, 0x0,
    0x9, 0xf8, 0x0, 0x8f, 0xa0, 0x8, 0xfa, 0x0,
    0x5f, 0xa0, 0x0, 0x6, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x0, 0x9b, 0xbd, 0xfc, 0xbb, 0x6f, 0xff,
    0xff, 0xff, 0xfb, 0x13, 0x3a, 0xf7, 0x33, 0x10,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x39, 0x10, 0x0,

    /* U+F068 "" */
    0xbd, 0xdd, 0xdd, 0xdd, 0x8e, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x4, 0x8a, 0x95, 0x0, 0x0, 0x1, 0xcf,
    0x84, 0x6e, 0xe3, 0x0, 0x1e, 0xf5, 0x8, 0x72,
    0xff, 0x40, 0xbf, 0xe0, 0x2d, 0xf5, 0xbf, 0xe0,
    0xdf, 0xe3, 0xff, 0xf6, 0xaf, 0xf1, 0x4f, 0xf3,
    0xaf, 0xd1, 0xef, 0x70, 0x5, 0xfd, 0x31, 0x2b,
    0xf7, 0x0, 0x0, 0x19, 0xdf, 0xea, 0x30, 0x0,

    /* U+F070 "" */
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x16, 0xaa, 0x83, 0x0, 0x0, 0x3, 0xef, 0xe6,
    0x49, 0xfb, 0x0, 0x0, 0x1, 0xbe, 0x49, 0x28,
    0xfd, 0x0, 0x1d, 0x40, 0x8f, 0xfe, 0x1f, 0xf9,
    0x4, 0xff, 0x50, 0x5f, 0xf1, 0xff, 0xb0, 0xa,
    0xfc, 0x0, 0x2d, 0xdf, 0xf2, 0x0, 0xa, 0xfa,
    0x10, 0x1b, 0xf7, 0x0, 0x0, 0x4, 0xbe, 0xe4,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x2, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xd, 0xe7, 0xcf,
    0x20, 0x0, 0x0, 0x7, 0xfc, 0x8, 0xfb, 0x0,
    0x0, 0x1, 0xef, 0xd0, 0x9f, 0xf4, 0x0, 0x0,
    0x9f, 0xff, 0x5c, 0xff, 0xd0, 0x0, 0x2f, 0xff,
    0xe1, 0xaf, 0xff, 0x60, 0xb, 0xff, 0xfe, 0x2b,
    0xff, 0xfe, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x1, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xc1, 0xef, 0xd1, 0x3, 0xef, 0xfc, 0x99,
    0xfb, 0x2e, 0xec, 0xf8, 0x0, 0x54, 0xde, 0x25,
    0x70, 0x0, 0xc, 0xf4, 0x1, 0x10, 0x0, 0xbf,
    0x5c, 0x78, 0xd1, 0xff, 0xf6, 0xa, 0xff, 0xfd,
    0x78, 0x60, 0x0, 0x7c, 0xf6, 0x0, 0x0, 0x0,
    0x5, 0x60,

    /* U+F077 "" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0xaf, 0x60,
    0x0, 0x0, 0xaf, 0xef, 0x60, 0x0, 0xaf, 0x90,
    0xcf, 0x60, 0x9f, 0x80, 0x0, 0xcf, 0x57, 0x80,
    0x0, 0x0, 0xa4,

    /* U+F078 "" */
    0x11, 0x0, 0x0, 0x2, 0xc, 0xe2, 0x0, 0x5,
    0xf8, 0x3f, 0xe2, 0x5, 0xfd, 0x10, 0x3f, 0xe7,
    0xfd, 0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x3b, 0x10, 0x0,

    /* U+F079 "" */
    0x0, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xc3, 0xff, 0xff, 0xf5, 0x0, 0xbe, 0xfe, 0xb3,
    0x44, 0x4e, 0x60, 0x4, 0x3f, 0x34, 0x0, 0x0,
    0xd6, 0x0, 0x2, 0xf2, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x2f, 0x20, 0x0, 0x8c, 0xea, 0xf1, 0x1,
    0xff, 0xff, 0xf7, 0xdf, 0xf7, 0x0, 0x4, 0x44,
    0x44, 0x11, 0xc7, 0x0,

    /* U+F07B "" */
    0x58, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff, 0xe4,
    0x44, 0x41, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F093 "" */
    0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x40, 0x0, 0x0, 0x3f, 0xff, 0xf4, 0x0, 0x2,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x36, 0xff, 0x63,
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x9a, 0xa5, 0xff, 0x5a, 0xa9,
    0xff, 0xff, 0xdd, 0xfe, 0xdf, 0xff, 0xff, 0xff,
    0xfc, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0xa8, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x39, 0x10, 0x4f, 0xf4, 0x0, 0xbf,
    0xfc, 0x9f, 0xf6, 0x0, 0xd, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x9f, 0xfd, 0x81, 0x0, 0x0, 0x1,
    0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x30, 0x5,
    0xc6, 0xe7, 0xbb, 0x5, 0xff, 0x4d, 0xbd, 0xb4,
    0xff, 0x40, 0x3c, 0xff, 0xff, 0x40, 0x0, 0x8,
    0xff, 0xb0, 0x0, 0x6f, 0xff, 0xdf, 0x80, 0xe,
    0x7b, 0xb2, 0xef, 0x80, 0xdb, 0xd9, 0x2, 0xef,
    0x73, 0xca, 0x10, 0x2, 0x72,

    /* U+F0C5 "" */
    0x0, 0x5d, 0xdd, 0x48, 0x0, 0x8, 0xff, 0xf6,
    0xf8, 0xcc, 0x8f, 0xff, 0x84, 0x3f, 0xe8, 0xff,
    0xff, 0xfc, 0xfe, 0x8f, 0xff, 0xff, 0xcf, 0xe8,
    0xff, 0xff, 0xfc, 0xfe, 0x8f, 0xff, 0xff, 0xcf,
    0xe7, 0xff, 0xff, 0xfc, 0xff, 0x46, 0x66, 0x66,
    0x3f, 0xff, 0xff, 0xf4, 0x0, 0x34, 0x44, 0x43,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x2, 0x22, 0x22, 0x0, 0xe, 0xff, 0xff, 0xfe,
    0x20, 0xf5, 0x22, 0x22, 0xfe, 0x1f, 0x40, 0x0,
    0xe, 0xf8, 0xf7, 0x44, 0x44, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xf6, 0xc, 0xff, 0x9f,
    0xff, 0x20, 0x9f, 0xf9, 0xff, 0xfc, 0x7f, 0xff,
    0x9a, 0xdd, 0xdd, 0xdd, 0xd4,

    /* U+F0C9 "" */
    0x67, 0x77, 0x77, 0x77, 0x4e, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x6, 0x77, 0x77,
    0x77, 0x74, 0xef, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x45, 0x55, 0x55, 0x55, 0x3f,
    0xff, 0xff, 0xff, 0xfb, 0x11, 0x11, 0x11, 0x11,
    0x0,

    /* U+F0E0 "" */
    0x58, 0x88, 0x88, 0x88, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6f, 0xff, 0xff, 0xff, 0xf6, 0xc5,
    0xdf, 0xff, 0xfd, 0x5c, 0xfe, 0x6a, 0xff, 0xa5,
    0xef, 0xff, 0xf9, 0x55, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F0E7 "" */
    0x3, 0xaa, 0xa2, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0xb, 0xff, 0xd8, 0x81,
    0xe, 0xff, 0xff, 0xe1, 0xe, 0xff, 0xff, 0x60,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0xd, 0xa0, 0x0, 0x0, 0xf, 0x20, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+F0EA "" */
    0x1, 0x79, 0x11, 0x0, 0xf, 0xfc, 0x9f, 0xf4,
    0x0, 0xff, 0xfd, 0xcc, 0x30, 0xf, 0xfa, 0x79,
    0x93, 0x40, 0xff, 0x8e, 0xff, 0x6f, 0x5f, 0xf8,
    0xef, 0xf7, 0x64, 0xff, 0x8e, 0xff, 0xff, 0xcf,
    0xf8, 0xef, 0xff, 0xfc, 0x46, 0x3e, 0xff, 0xff,
    0xc0, 0x0, 0xdf, 0xff, 0xfc, 0x0, 0x2, 0x44,
    0x44, 0x20,

    /* U+F0F3 "" */
    0x0, 0x1, 0x90, 0x0, 0x0, 0x2, 0xaf, 0x81,
    0x0, 0x2, 0xff, 0xff, 0xd0, 0x0, 0x9f, 0xff,
    0xff, 0x50, 0xc, 0xff, 0xff, 0xf8, 0x0, 0xef,
    0xff, 0xff, 0xa0, 0x3f, 0xff, 0xff, 0xfe, 0xd,
    0xff, 0xff, 0xff, 0xf9, 0x46, 0x66, 0x66, 0x66,
    0x20, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0,

    /* U+F11C "" */
    0x24, 0x44, 0x44, 0x44, 0x44, 0x30, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xf4, 0xa0, 0xa0, 0xb0,
    0xb0, 0xf4, 0xff, 0xbe, 0xae, 0xae, 0xaf, 0xf4,
    0xff, 0x3a, 0xa, 0xa, 0xf, 0xf4, 0xfb, 0xea,
    0xaa, 0xaa, 0xea, 0xf4, 0xf7, 0xb4, 0x44, 0x44,
    0xc4, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe1,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x29, 0x70, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xe0, 0x0, 0x0, 0x4b, 0xff,
    0xff, 0x70, 0x0, 0x5d, 0xff, 0xff, 0xff, 0x10,
    0xc, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xa, 0xee,
    0xef, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0,

    /* U+F15B "" */
    0xef, 0xff, 0x5b, 0x0, 0xff, 0xff, 0x6f, 0xb0,
    0xff, 0xff, 0x68, 0x83, 0xff, 0xff, 0xfd, 0xd6,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x5,
    0xbf, 0xff, 0xfd, 0x81, 0x0, 0x2c, 0xfe, 0xa8,
    0x78, 0xcf, 0xf7, 0xd, 0xf7, 0x0, 0x0, 0x0,
    0x3c, 0xf5, 0x22, 0x5, 0xbe, 0xfd, 0x81, 0x5,
    0x0, 0x9, 0xfe, 0xa9, 0xcf, 0xe2, 0x0, 0x0,
    0x37, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0x10, 0x0,
    0x0,

    /* U+F240 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x55, 0x55, 0x5a, 0xf2, 0xf6, 0xff, 0xff,
    0xff, 0xfd, 0x4f, 0x5f, 0x6f, 0xff, 0xff, 0xff,
    0xd1, 0xf5, 0xf5, 0x77, 0x77, 0x77, 0x76, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F241 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x55, 0x54, 0x4a, 0xf2, 0xf6, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0x5f, 0x6f, 0xff, 0xff, 0xf0,
    0x1, 0xf5, 0xf5, 0x77, 0x77, 0x77, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F242 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x54, 0x44, 0x4a, 0xf2, 0xf6, 0xff, 0xff,
    0x20, 0x0, 0x4f, 0x5f, 0x6f, 0xff, 0xf2, 0x0,
    0x1, 0xf5, 0xf5, 0x77, 0x77, 0x10, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F243 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x54, 0x44, 0x44, 0x4a, 0xf2, 0xf6, 0xff, 0x50,
    0x0, 0x0, 0x4f, 0x5f, 0x6f, 0xf5, 0x0, 0x0,
    0x1, 0xf5, 0xf5, 0x77, 0x20, 0x0, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F244 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf, 0x74,
    0x44, 0x44, 0x44, 0x4a, 0xf2, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x5f, 0x40, 0x0, 0x0, 0x0,
    0x1, 0xf5, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x5b, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xbd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xa1,
    0x3, 0x0, 0x0, 0xa, 0xf7, 0x39, 0x0, 0x0,
    0x7, 0x60, 0xff, 0xea, 0xbf, 0xaa, 0xaa, 0xdf,
    0x45, 0xa3, 0x0, 0x93, 0x0, 0x4, 0x10, 0x0,
    0x0, 0x1, 0xb8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x6b, 0xca, 0x40, 0x0, 0x9f, 0xf6, 0xff,
    0x40, 0x1f, 0xff, 0x26, 0xfb, 0x4, 0xf6, 0xb4,
    0x6b, 0xf0, 0x6f, 0xf4, 0x6, 0xff, 0x6, 0xff,
    0x90, 0xbf, 0xf0, 0x5f, 0x95, 0x34, 0xcf, 0x2,
    0xfb, 0xf3, 0x4d, 0xc0, 0xc, 0xff, 0x3d, 0xf7,
    0x0, 0x1b, 0xfe, 0xf9, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0,

    /* U+F2ED "" */
    0x12, 0x3b, 0xca, 0x22, 0x1f, 0xff, 0xff, 0xff,
    0xfb, 0x36, 0x66, 0x66, 0x66, 0x16, 0xff, 0xff,
    0xff, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f, 0x26, 0xf6,
    0xf6, 0xf7, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f, 0x26,
    0xf6, 0xf6, 0xf7, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f,
    0x24, 0xff, 0xff, 0xff, 0xf1, 0x3, 0x44, 0x44,
    0x42, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x97, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0xa5, 0xef,
    0xe0, 0x0, 0x0, 0xbf, 0xe5, 0xd4, 0x0, 0x0,
    0xbf, 0xff, 0xe0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xff, 0xf4, 0x0, 0x0, 0xd, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0x88, 0x88, 0x88, 0x86, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x8, 0xff, 0xf9,
    0x6f, 0x69, 0xff, 0x88, 0xff, 0xff, 0xc1, 0x21,
    0xcf, 0xf8, 0xdf, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0x82, 0xef, 0xff, 0x71, 0x91, 0x7f, 0xf8, 0x2,
    0xef, 0xfe, 0xdf, 0xde, 0xff, 0x70, 0x2, 0xdf,
    0xff, 0xff, 0xff, 0xe3,

    /* U+F7C2 "" */
    0x1, 0xdf, 0xff, 0xe5, 0x1d, 0x6c, 0x5a, 0xab,
    0xdf, 0x3b, 0x18, 0x8b, 0xff, 0xdf, 0xde, 0xeb,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0xbf, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0x0, 0x8, 0x10,
    0x0, 0x7, 0xf0, 0xb, 0xf2, 0x0, 0x0, 0x8f,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xfa,
    0x99, 0x99, 0x99, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+FF1A "：" */
    0x12, 0x6a, 0x0, 0x0, 0x1, 0x6a, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 43, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 39, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7, .adv_w = 60, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 13, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 38, .adv_w = 93, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 68, .adv_w = 128, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 96, .adv_w = 115, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 124, .adv_w = 32, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 127, .adv_w = 57, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 145, .adv_w = 57, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 159, .adv_w = 70, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 169, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 184, .adv_w = 40, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 190, .adv_w = 78, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 193, .adv_w = 39, .box_w = 2, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 195, .adv_w = 64, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 209, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 230, .adv_w = 93, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 244, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 265, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 286, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 307, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 328, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 349, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 370, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 391, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 412, .adv_w = 42, .box_w = 2, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 417, .adv_w = 44, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 428, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 443, .adv_w = 93, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 452, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 467, .adv_w = 72, .box_w = 6, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 488, .adv_w = 157, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 533, .adv_w = 109, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 558, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 583, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 608, .adv_w = 116, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 633, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 654, .adv_w = 91, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 675, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 700, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 725, .adv_w = 44, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 732, .adv_w = 77, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 750, .adv_w = 112, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 775, .adv_w = 91, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 796, .adv_w = 139, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 824, .adv_w = 120, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 849, .adv_w = 123, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 877, .adv_w = 97, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 898, .adv_w = 123, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 934, .adv_w = 105, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 959, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 980, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1001, .adv_w = 118, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1026, .adv_w = 108, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1051, .adv_w = 157, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1086, .adv_w = 108, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1111, .adv_w = 102, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1136, .adv_w = 93, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1157, .adv_w = 57, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1175, .adv_w = 64, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1189, .adv_w = 57, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1203, .adv_w = 79, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1213, .adv_w = 70, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1216, .adv_w = 51, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1219, .adv_w = 88, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1232, .adv_w = 98, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1253, .adv_w = 79, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1266, .adv_w = 98, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1287, .adv_w = 88, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1302, .adv_w = 57, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1316, .adv_w = 98, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1337, .adv_w = 94, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1358, .adv_w = 40, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1365, .adv_w = 40, .box_w = 4, .box_h = 9, .ofs_x = -2, .ofs_y = -2},
    {.bitmap_index = 1383, .adv_w = 87, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1404, .adv_w = 40, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1411, .adv_w = 138, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1431, .adv_w = 94, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1446, .adv_w = 95, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1461, .adv_w = 98, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1482, .adv_w = 98, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1503, .adv_w = 63, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1513, .adv_w = 73, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1526, .adv_w = 62, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1540, .adv_w = 94, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1555, .adv_w = 85, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1570, .adv_w = 127, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1590, .adv_w = 82, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1605, .adv_w = 86, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1626, .adv_w = 76, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1639, .adv_w = 61, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1657, .adv_w = 32, .box_w = 2, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1666, .adv_w = 61, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1684, .adv_w = 93, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1690, .adv_w = 51, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1696, .adv_w = 160, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1732, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1772, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1817, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1867, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1917, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1962, .adv_w = 160, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2003, .adv_w = 160, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2048, .adv_w = 160, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2093, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2143, .adv_w = 160, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2188, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2233, .adv_w = 160, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2269, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2319, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2364, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2414, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2469, .adv_w = 160, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2524, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2574, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2624, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2674, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2724, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2779, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2834, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2879, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2929, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2979, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3024, .adv_w = 160, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3069, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3124, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3169, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3219, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3269, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3324, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3379, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3429, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3474, .adv_w = 160, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3519, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3569, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3619, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3669, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3724, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3774, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3829, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3884, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3939, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3989, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4050, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4090, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4140, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4180, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4208, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4263, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4318, .adv_w = 180, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4378, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4433, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4481, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4536, .adv_w = 80, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4556, .adv_w = 120, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4588, .adv_w = 180, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4648, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4688, .adv_w = 110, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4727, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4762, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4812, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4857, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4902, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4937, .adv_w = 140, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4987, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5017, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5047, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5092, .adv_w = 140, .box_w = 9, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 5106, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5154, .adv_w = 200, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5226, .adv_w = 180, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5298, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5348, .adv_w = 140, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5375, .adv_w = 140, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5402, .adv_w = 200, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5454, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5494, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5549, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5610, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5655, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5705, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5750, .adv_w = 140, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5791, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5831, .adv_w = 100, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5875, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5925, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5975, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6023, .adv_w = 160, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6089, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6133, .adv_w = 200, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6198, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6244, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6290, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6336, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6382, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6428, .adv_w = 200, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6487, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6537, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6587, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6648, .adv_w = 200, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6700, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6744, .adv_w = 161, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6783, .adv_w = 160, .box_w = 2, .box_h = 7, .ofs_x = 1, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x4d7d, 0x4ddc, 0x4e46, 0x4e9d, 0x4f31, 0x514b, 0x5186,
    0x51eb, 0x51f0, 0x5297, 0x52c0, 0x52db, 0x564a, 0x5857, 0x5b49,
    0x5df6, 0x5f51, 0x5fbf, 0x61a3, 0x62f7, 0x636c, 0x668a, 0x6c91,
    0x6d79, 0x7009, 0x71a9, 0x7206, 0x72ff, 0x7485, 0x7739, 0x7751,
    0x7c4b, 0x7c72, 0x7df7, 0x7e2f, 0x7e44, 0x7ebe, 0x842d, 0x879c,
    0x8b0e, 0x8b2d, 0x8eb8, 0x8ecd, 0x8f20, 0x8f49, 0x8f6a, 0x8f6f,
    0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61, 0xef63, 0xef65,
    0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78, 0xef8e, 0xef93,
    0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2, 0xefa3, 0xefa4,
    0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4, 0xefc7, 0xefc8,
    0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015, 0xf017, 0xf019,
    0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074, 0xf0ab, 0xf13b,
    0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7, 0xf1e3, 0xf23d,
    0xf254, 0xf4aa, 0xf712, 0xf7f2, 0xfe6a
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 65131, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 109, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, 0, 3, 3,
    2, 0, 2, 0, 0, 11, 0, 0,
    3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 3, 3, -7,
    -23, -15, 4, -6, 0, -19, -2, 3,
    0, 0, 0, 0, 0, 0, -12, 0,
    -12, -4, 0, -8, -9, -1, -8, -7,
    -9, -7, -9, 0, 0, 0, -5, -16,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, -7, -6,
    0, 0, 0, -6, 0, -5, 0, -6,
    -3, -6, -9, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, -19, 0, -10, 4, 0, -11,
    -6, 0, 0, 0, -13, -2, -15, -11,
    0, -18, 3, 0, 0, -2, 0, 0,
    0, 0, 0, 0, -7, 0, -7, 0,
    0, -2, 0, 0, 0, -2, 0, 0,
    0, 2, 0, -6, 0, -7, -3, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, -5, 4, 0, 5, -3, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -4, 0, 0, 0,
    0, 0, 2, 0, 2, -2, 0, 2,
    0, 0, 0, -2, 0, 0, -2, 0,
    -2, 0, -2, -2, 0, 0, -1, -2,
    -1, -3, -1, -3, 0, -2, 4, 0,
    1, -21, -9, 7, -1, 0, -22, 0,
    4, 0, 0, 0, 0, 0, 0, -6,
    0, -4, -1, 0, -3, 0, -2, 0,
    -4, -6, -4, -4, 0, 0, 0, 0,
    3, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, -2,
    0, 0, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, -17, 3, 0, -1,
    -9, -2, 0, -3, 0, -4, 0, 0,
    0, 0, 0, -5, 0, -5, -7, 0,
    -2, -2, -7, -7, -11, -5, -11, 0,
    -8, -16, 0, -14, 4, 0, -11, -8,
    0, 3, -1, -21, -7, -24, -18, 0,
    -29, 0, -1, 0, -3, -3, 0, 0,
    0, -4, -4, -15, 0, -15, 0, -2,
    2, 0, 2, -23, -13, 3, 0, 0,
    -26, 0, 0, 0, 0, -1, -4, 0,
    -5, -5, 0, -5, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, -2,
    0, -1, 6, 0, -1, -2, 0, 0,
    1, -2, -2, -4, -3, 0, -8, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 4,
    0, 0, -2, 0, 0, -4, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 5, 0, -13,
    -18, -13, 6, -5, 0, -22, 0, 4,
    0, 3, 3, 0, 0, 0, -19, 0,
    -18, -7, 0, -15, -18, -5, -14, -17,
    -17, -17, -14, -2, 3, 0, -4, -12,
    -11, 0, -3, 0, -12, 0, 3, 0,
    0, 0, 0, 0, 0, -12, 0, -10,
    -3, 0, -7, -7, 0, -6, -4, -5,
    -4, -5, 0, 0, 3, -15, 2, 0,
    2, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, -6, 0,
    0, -3, -3, -5, -5, -10, 0, -10,
    0, -5, 2, 3, -12, -22, -18, 1,
    -9, 0, -22, -4, 0, 0, 0, 0,
    0, 0, 0, -18, 0, -17, -8, 0,
    -14, -15, -5, -12, -12, -11, -12, -12,
    0, 0, 2, -7, 3, 0, 2, -4,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -2, 0, 0, 0,
    0, 0, 0, -5, 0, -5, 0, 0,
    -7, 0, 0, 0, 0, -5, 0, 0,
    0, 0, -14, 0, -12, -11, -1, -16,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -2, 0, 0, -8,
    0, -2, -5, 0, -7, 0, 0, 0,
    0, -18, 0, -12, -10, -6, -17, 0,
    -2, 0, 0, -1, 0, 0, 0, -1,
    0, -3, -4, -3, -4, 0, 1, 0,
    3, 4, 0, -2, 0, 0, 0, 0,
    -12, 0, -8, -5, 3, -12, 0, 0,
    0, -1, 2, 0, 0, 0, 4, 0,
    0, 1, 0, 2, 0, 0, 2, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 2, 0,
    0, -4, 0, 0, 0, 0, -12, 0,
    -10, -8, -2, -15, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, -1,
    0, 0, 0, 8, 0, -1, -12, 0,
    7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, -4, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, -15, 0, -8, -7,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 7, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, -4, 4, 0, -8, 0, 0,
    0, 0, -15, 0, -10, -9, 0, -14,
    0, -5, 0, -4, 0, 0, 0, -2,
    0, -1, 0, 0, 0, 0, 0, 4,
    0, 1, -17, -8, -5, 0, 0, -19,
    0, 0, 0, -7, 0, -8, -12, 0,
    -7, 0, -5, 0, 0, 0, -1, 5,
    0, 0, 0, 0, 0, 0, -5, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    -16, 0, -12, -8, -1, -17, 0, 0,
    0, 0, 0, 0, 0, 1, 0, 0,
    -1, 0, -1, 2, 0, -1, 2, 0,
    4, 0, -4, 0, 0, 0, 0, -11,
    0, -8, 0, 0, -11, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -15,
    -7, -4, 0, 0, -13, 0, -17, 0,
    -7, -4, -10, -12, 0, -3, 0, -3,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 1, 0,
    -6, 0, 0, 0, 0, -17, 0, -9,
    -5, 0, -11, 0, -3, 0, -4, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, -5,
    0, 0, 0, 0, -16, 0, -9, -5,
    0, -12, 0, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_10 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_10 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 10,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_10*/

