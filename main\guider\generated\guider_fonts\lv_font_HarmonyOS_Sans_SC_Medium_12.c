/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_12
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_12 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x2f, 0x12, 0xf0, 0x1f, 0x1, 0xf0, 0x1f, 0x0,
    0xf0, 0xe, 0x0, 0x20, 0x1a, 0x2, 0xe1,

    /* U+0022 "\"" */
    0x25, 0x16, 0x5b, 0x3d, 0x5b, 0x3d, 0x4b, 0x3c,

    /* U+0023 "#" */
    0x0, 0x1f, 0x2, 0xf0, 0x0, 0x4d, 0x5, 0xc0,
    0x0, 0x7a, 0x8, 0xa0, 0xf, 0xff, 0xff, 0xfc,
    0x3, 0xe6, 0x3f, 0x52, 0x3, 0xe0, 0x4e, 0x0,
    0xdf, 0xff, 0xff, 0xf0, 0x2b, 0x93, 0xc8, 0x30,
    0xd, 0x40, 0xe3, 0x0, 0xf, 0x11, 0xf0, 0x0,

    /* U+0024 "$" */
    0x0, 0x7, 0x10, 0x0, 0x0, 0xf2, 0x0, 0x2,
    0xaf, 0xb3, 0x1, 0xec, 0xfd, 0xf1, 0x5e, 0xf,
    0x2b, 0x35, 0xf1, 0xf2, 0x0, 0xc, 0xef, 0x30,
    0x0, 0x7, 0xff, 0x80, 0x0, 0xf, 0x7f, 0x66,
    0x70, 0xf2, 0xb8, 0x5f, 0x5f, 0x5e, 0x50, 0x8f,
    0xff, 0xa0, 0x0, 0x1f, 0x30, 0x0, 0x0, 0xf2,
    0x0,

    /* U+0025 "%" */
    0x1c, 0xf6, 0x0, 0x8c, 0x0, 0x9a, 0x4f, 0x11,
    0xf4, 0x0, 0xb5, 0xd, 0x38, 0xb0, 0x0, 0xa8,
    0x1f, 0x3f, 0x30, 0x0, 0x2e, 0xf8, 0x9b, 0x0,
    0x0, 0x0, 0x12, 0xf2, 0x0, 0x0, 0x0, 0xa,
    0xa1, 0xbe, 0x80, 0x0, 0x2f, 0x29, 0xb3, 0xe4,
    0x0, 0xb9, 0x9, 0xa1, 0xd4, 0x3, 0xf1, 0x1,
    0xce, 0x90,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0x70,
    0x0, 0x3, 0xf6, 0x6f, 0x30, 0x0, 0x5f, 0x0,
    0xf4, 0x0, 0x1, 0xf9, 0xcb, 0x0, 0x0, 0x1c,
    0xfa, 0x4, 0x90, 0xd, 0xb8, 0xe2, 0x8b, 0x6,
    0xf0, 0xa, 0xdd, 0x60, 0x7e, 0x0, 0xd, 0xf1,
    0x3, 0xf7, 0x25, 0xff, 0x90, 0x6, 0xdf, 0xd5,
    0x3f, 0x60,

    /* U+0027 "'" */
    0x25, 0x5b, 0x5b, 0x4b,

    /* U+0028 "(" */
    0x0, 0x3, 0x0, 0x9, 0x90, 0x3, 0xf1, 0x0,
    0xa9, 0x0, 0xe, 0x50, 0x1, 0xf2, 0x0, 0x3f,
    0x10, 0x2, 0xf1, 0x0, 0x1f, 0x30, 0x0, 0xd6,
    0x0, 0x9, 0xb0, 0x0, 0x2f, 0x20, 0x0, 0x7b,
    0x0,

    /* U+0029 ")" */
    0x21, 0x0, 0x5d, 0x0, 0xc, 0x70, 0x5, 0xe0,
    0x1, 0xf3, 0x0, 0xe5, 0x0, 0xd7, 0x0, 0xd6,
    0x0, 0xe5, 0x1, 0xf2, 0x6, 0xd0, 0xd, 0x60,
    0x7b, 0x0,

    /* U+002A "*" */
    0x0, 0xd1, 0x6, 0xcd, 0x9a, 0x9, 0xfc, 0x6,
    0xcd, 0xaa, 0x0, 0xd1, 0x10, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x5, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x1f, 0x0, 0x6, 0xff, 0xff, 0xf6, 0x13, 0x4f,
    0x33, 0x10, 0x1, 0xf0, 0x0, 0x0, 0x1f, 0x0,
    0x0,

    /* U+002C "," */
    0xa, 0x21, 0xe6, 0x8, 0x43, 0x90, 0x0, 0x0,

    /* U+002D "-" */
    0x4f, 0xff, 0xf2, 0x2, 0x22, 0x20,

    /* U+002E "." */
    0x1a, 0x2, 0xe1,

    /* U+002F "/" */
    0x0, 0xb, 0x80, 0x1, 0xf2, 0x0, 0x5d, 0x0,
    0xb, 0x80, 0x0, 0xf2, 0x0, 0x5d, 0x0, 0xa,
    0x80, 0x0, 0xf3, 0x0, 0x5d, 0x0, 0xa, 0x80,
    0x0,

    /* U+0030 "0" */
    0x2, 0xcf, 0xc1, 0x0, 0xcc, 0x5d, 0xb0, 0x2f,
    0x30, 0x4f, 0x25, 0xf0, 0x0, 0xf5, 0x6f, 0x0,
    0xf, 0x66, 0xf0, 0x0, 0xf6, 0x5f, 0x0, 0xf,
    0x52, 0xf3, 0x4, 0xf2, 0xc, 0xc5, 0xdc, 0x0,
    0x2c, 0xfc, 0x10,

    /* U+0031 "1" */
    0x3, 0xdb, 0x7f, 0xeb, 0xb4, 0x9b, 0x0, 0x9b,
    0x0, 0x9b, 0x0, 0x9b, 0x0, 0x9b, 0x0, 0x9b,
    0x0, 0x9b, 0x0, 0x9b,

    /* U+0032 "2" */
    0x3, 0xcf, 0xc2, 0x1, 0xea, 0x5d, 0xc0, 0x2b,
    0x0, 0x5f, 0x0, 0x0, 0x6, 0xf0, 0x0, 0x0,
    0xda, 0x0, 0x0, 0x8f, 0x20, 0x0, 0x5f, 0x50,
    0x0, 0x2f, 0x80, 0x0, 0x1d, 0xd4, 0x44, 0x16,
    0xff, 0xff, 0xf5,

    /* U+0033 "3" */
    0x4, 0xdf, 0xc2, 0x1, 0xf9, 0x5d, 0xc0, 0x17,
    0x0, 0x6f, 0x0, 0x0, 0x1c, 0xc0, 0x0, 0x7f,
    0xf3, 0x0, 0x1, 0x4c, 0xe0, 0x0, 0x0, 0x2f,
    0x42, 0x90, 0x2, 0xf4, 0x2f, 0x94, 0xbe, 0x0,
    0x4d, 0xfc, 0x30,

    /* U+0034 "4" */
    0x0, 0x9, 0xc0, 0x0, 0x1, 0xf5, 0x0, 0x0,
    0x7e, 0x0, 0x0, 0xe, 0x70, 0x0, 0x5, 0xf1,
    0x97, 0x0, 0xc9, 0xb, 0x90, 0x3f, 0x20, 0xb9,
    0x8, 0xff, 0xff, 0xfc, 0x13, 0x33, 0xca, 0x20,
    0x0, 0xb, 0x90,

    /* U+0035 "5" */
    0x7, 0xff, 0xfe, 0x0, 0x9b, 0x33, 0x30, 0xa,
    0x90, 0x0, 0x0, 0xc7, 0x0, 0x0, 0xe, 0xef,
    0xd3, 0x0, 0xe8, 0x4b, 0xe0, 0x0, 0x0, 0x2f,
    0x30, 0x30, 0x2, 0xf3, 0xf, 0x94, 0xce, 0x0,
    0x4d, 0xfc, 0x20,

    /* U+0036 "6" */
    0x0, 0x8, 0xd0, 0x0, 0x2, 0xf5, 0x0, 0x0,
    0xad, 0x0, 0x0, 0x3f, 0x50, 0x0, 0xb, 0xff,
    0xe5, 0x2, 0xfa, 0x49, 0xf2, 0x4f, 0x10, 0xf,
    0x64, 0xf1, 0x0, 0xf6, 0xe, 0xb5, 0xaf, 0x10,
    0x2c, 0xfd, 0x40,

    /* U+0037 "7" */
    0x5f, 0xff, 0xff, 0x41, 0x33, 0x37, 0xf1, 0x0,
    0x0, 0xab, 0x0, 0x0, 0xf, 0x50, 0x0, 0x5,
    0xf0, 0x0, 0x0, 0xba, 0x0, 0x0, 0x1f, 0x40,
    0x0, 0x7, 0xe0, 0x0, 0x0, 0xc9, 0x0, 0x0,
    0x2f, 0x40, 0x0,

    /* U+0038 "8" */
    0x3, 0xcf, 0xc2, 0x0, 0xeb, 0x4b, 0xd0, 0x1f,
    0x30, 0x3f, 0x0, 0xe9, 0x19, 0xd0, 0x4, 0xff,
    0xf4, 0x1, 0xea, 0x4a, 0xe0, 0x5f, 0x0, 0xf,
    0x56, 0xf0, 0x0, 0xf5, 0x2f, 0xa4, 0xaf, 0x10,
    0x4d, 0xfd, 0x40,

    /* U+0039 "9" */
    0x4, 0xdf, 0xc2, 0x1, 0xfa, 0x5c, 0xd0, 0x6f,
    0x0, 0x2f, 0x37, 0xe0, 0x1, 0xf4, 0x3f, 0x71,
    0x9f, 0x10, 0x8f, 0xff, 0xb0, 0x0, 0x16, 0xf2,
    0x0, 0x0, 0xca, 0x0, 0x0, 0x5f, 0x10, 0x0,
    0xd, 0x80, 0x0,

    /* U+003A ":" */
    0x1e, 0x30, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa1, 0x1e, 0x30,

    /* U+003B ";" */
    0xd, 0x40, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa3, 0xe, 0x80, 0x75, 0x2a, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x29, 0x50, 0x3, 0xaf, 0xb2, 0x2c,
    0xfa, 0x30, 0x5, 0xfc, 0x30, 0x0, 0x3, 0xaf,
    0xb4, 0x0, 0x0, 0x29, 0xf5, 0x0, 0x0, 0x1,
    0x20,

    /* U+003D "=" */
    0x6f, 0xff, 0xff, 0x61, 0x33, 0x33, 0x31, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf6, 0x13, 0x33,
    0x33, 0x10,

    /* U+003E ">" */
    0x59, 0x20, 0x0, 0x2, 0xcf, 0xa3, 0x0, 0x0,
    0x3a, 0xfb, 0x20, 0x0, 0x3c, 0xf5, 0x4, 0xbf,
    0xa3, 0x6, 0xf9, 0x20, 0x0, 0x21, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x1, 0xbf, 0xe5, 0x0, 0xcc, 0x5a, 0xf2, 0x6,
    0x10, 0x2f, 0x30, 0x0, 0x9, 0xd0, 0x0, 0x8,
    0xd1, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x10, 0x0,
    0x1, 0xe3, 0x0,

    /* U+0040 "@" */
    0x0, 0x4, 0xbd, 0xec, 0x60, 0x0, 0x0, 0x8e,
    0x73, 0x36, 0xdb, 0x0, 0x6, 0xd1, 0x0, 0x0,
    0xc, 0x80, 0xe, 0x30, 0x9f, 0xc9, 0x82, 0xe0,
    0x3d, 0x6, 0xe4, 0x6f, 0x80, 0xd3, 0x5a, 0xb,
    0x80, 0xb, 0x80, 0xb5, 0x6a, 0xb, 0x70, 0xa,
    0x80, 0xc4, 0x4c, 0x7, 0xc1, 0x2e, 0xa1, 0xf1,
    0xf, 0x20, 0xbf, 0xe5, 0xef, 0x80, 0x8, 0xb0,
    0x1, 0x0, 0x1, 0x0, 0x0, 0xcc, 0x40, 0x4,
    0x70, 0x0, 0x0, 0x8, 0xef, 0xfe, 0x70, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0xb, 0xc0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x5c, 0xc7, 0x0, 0x0, 0xb, 0x77,
    0xd0, 0x0, 0x1, 0xf2, 0x2f, 0x20, 0x0, 0x6d,
    0x0, 0xc8, 0x0, 0xb, 0xff, 0xff, 0xd0, 0x1,
    0xf5, 0x33, 0x4f, 0x30, 0x6d, 0x0, 0x0, 0xc9,
    0xc, 0x80, 0x0, 0x7, 0xe0,

    /* U+0042 "B" */
    0xff, 0xfe, 0xa1, 0xf, 0x73, 0x5d, 0xb0, 0xf4,
    0x0, 0x6f, 0xf, 0x40, 0x1c, 0xc0, 0xff, 0xff,
    0xf4, 0xf, 0x62, 0x3a, 0xf2, 0xf4, 0x0, 0xf,
    0x6f, 0x40, 0x0, 0xf6, 0xf7, 0x34, 0xaf, 0x2f,
    0xff, 0xfc, 0x40,

    /* U+0043 "C" */
    0x0, 0x4c, 0xfd, 0x60, 0x5, 0xf9, 0x58, 0xf5,
    0xe, 0x90, 0x0, 0x63, 0x4f, 0x20, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x0,
    0x4f, 0x20, 0x0, 0x0, 0xe, 0x90, 0x0, 0x62,
    0x5, 0xf9, 0x58, 0xf5, 0x0, 0x4c, 0xfd, 0x60,

    /* U+0044 "D" */
    0xff, 0xfe, 0x81, 0x0, 0xf7, 0x46, 0xed, 0x0,
    0xf4, 0x0, 0x1e, 0x80, 0xf4, 0x0, 0x7, 0xe0,
    0xf4, 0x0, 0x5, 0xf0, 0xf4, 0x0, 0x5, 0xf0,
    0xf4, 0x0, 0x7, 0xe0, 0xf4, 0x0, 0x1e, 0x80,
    0xf7, 0x46, 0xed, 0x0, 0xff, 0xfe, 0x91, 0x0,

    /* U+0045 "E" */
    0xff, 0xff, 0xf8, 0xf7, 0x44, 0x42, 0xf4, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0xff, 0xff, 0xf1, 0xf7,
    0x33, 0x30, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0xf7, 0x44, 0x42, 0xff, 0xff, 0xfa,

    /* U+0046 "F" */
    0xff, 0xff, 0xf8, 0xf7, 0x44, 0x42, 0xf4, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0xf5, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0xf7, 0x33, 0x30, 0xf4, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3c, 0xfe, 0x80, 0x4, 0xfa, 0x57, 0xf9,
    0xe, 0x90, 0x0, 0x44, 0x3f, 0x20, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x2f, 0xff,
    0x3f, 0x20, 0x3, 0x7f, 0xe, 0xa0, 0x0, 0x5f,
    0x4, 0xfb, 0x55, 0xcd, 0x0, 0x3b, 0xfe, 0xa1,

    /* U+0048 "H" */
    0xf4, 0x0, 0x3, 0xf0, 0xf4, 0x0, 0x3, 0xf0,
    0xf4, 0x0, 0x3, 0xf0, 0xf5, 0x0, 0x4, 0xf0,
    0xff, 0xff, 0xff, 0xf0, 0xf7, 0x33, 0x36, 0xf0,
    0xf4, 0x0, 0x3, 0xf0, 0xf4, 0x0, 0x3, 0xf0,
    0xf4, 0x0, 0x3, 0xf0, 0xf4, 0x0, 0x3, 0xf0,

    /* U+0049 "I" */
    0xf4, 0xf4, 0xf4, 0xf4, 0xf4, 0xf4, 0xf4, 0xf4,
    0xf4, 0xf4,

    /* U+004A "J" */
    0x0, 0x7, 0xd0, 0x0, 0x7d, 0x0, 0x7, 0xd0,
    0x0, 0x7d, 0x0, 0x7, 0xd0, 0x0, 0x7d, 0x0,
    0x7, 0xd2, 0x0, 0x8b, 0xda, 0x5e, 0x73, 0xdf,
    0xa0,

    /* U+004B "K" */
    0xf4, 0x0, 0x1e, 0x90, 0xf4, 0x0, 0xcc, 0x0,
    0xf4, 0xa, 0xd1, 0x0, 0xf4, 0x6f, 0x20, 0x0,
    0xf8, 0xfc, 0x0, 0x0, 0xff, 0xaf, 0x60, 0x0,
    0xfa, 0x9, 0xe1, 0x0, 0xf4, 0x0, 0xeb, 0x0,
    0xf4, 0x0, 0x4f, 0x50, 0xf4, 0x0, 0xa, 0xe1,

    /* U+004C "L" */
    0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0xf7, 0x44, 0x42, 0xff, 0xff, 0xf8,

    /* U+004D "M" */
    0xf5, 0x0, 0x0, 0xd, 0x6f, 0xe0, 0x0, 0x6,
    0xf6, 0xff, 0x70, 0x0, 0xef, 0x6f, 0x9f, 0x10,
    0x8c, 0xd6, 0xf3, 0xc9, 0x1f, 0x3d, 0x6f, 0x33,
    0xfb, 0xa0, 0xd6, 0xf3, 0xa, 0xf1, 0xd, 0x6f,
    0x30, 0x14, 0x0, 0xd6, 0xf3, 0x0, 0x0, 0xd,
    0x6f, 0x30, 0x0, 0x0, 0xd6,

    /* U+004E "N" */
    0xf6, 0x0, 0x4, 0xff, 0xe1, 0x0, 0x4f, 0xfe,
    0xa0, 0x4, 0xff, 0x6f, 0x40, 0x4f, 0xf3, 0x9d,
    0x4, 0xff, 0x30, 0xe8, 0x4f, 0xf3, 0x5, 0xf6,
    0xff, 0x30, 0xa, 0xff, 0xf3, 0x0, 0x1f, 0xff,
    0x30, 0x0, 0x6f,

    /* U+004F "O" */
    0x0, 0x4c, 0xfd, 0x70, 0x0, 0x5f, 0x95, 0x7f,
    0x90, 0xe, 0x90, 0x0, 0x5f, 0x24, 0xf2, 0x0,
    0x0, 0xe7, 0x6f, 0x0, 0x0, 0xb, 0x96, 0xf0,
    0x0, 0x0, 0xb9, 0x4f, 0x20, 0x0, 0xe, 0x70,
    0xe9, 0x0, 0x5, 0xf2, 0x5, 0xf9, 0x57, 0xf8,
    0x0, 0x5, 0xcf, 0xd7, 0x0,

    /* U+0050 "P" */
    0xff, 0xfe, 0x80, 0xf, 0x73, 0x6f, 0x90, 0xf4,
    0x0, 0x7e, 0xf, 0x40, 0x6, 0xf0, 0xf4, 0x3,
    0xdb, 0xf, 0xff, 0xfc, 0x10, 0xf7, 0x31, 0x0,
    0xf, 0x40, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4c, 0xfd, 0x70, 0x0, 0x5, 0xf9, 0x57,
    0xf8, 0x0, 0xe, 0x90, 0x0, 0x5f, 0x20, 0x4f,
    0x20, 0x0, 0xe, 0x70, 0x6f, 0x0, 0x0, 0xb,
    0x90, 0x6f, 0x0, 0x0, 0xb, 0x90, 0x4f, 0x20,
    0x0, 0xe, 0x70, 0xe, 0x90, 0x0, 0x5f, 0x20,
    0x5, 0xf9, 0x57, 0xf9, 0x0, 0x0, 0x5c, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x3, 0xeb, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xc1,

    /* U+0052 "R" */
    0xff, 0xfe, 0x80, 0xf, 0x73, 0x6f, 0x90, 0xf4,
    0x0, 0x7e, 0xf, 0x40, 0x6, 0xf0, 0xf4, 0x3,
    0xdb, 0xf, 0xff, 0xfc, 0x10, 0xf7, 0x3d, 0x90,
    0xf, 0x40, 0x4f, 0x40, 0xf4, 0x0, 0xad, 0xf,
    0x40, 0x1, 0xe8,

    /* U+0053 "S" */
    0x5, 0xdf, 0xd6, 0x2, 0xf9, 0x48, 0xf4, 0x6f,
    0x0, 0x6, 0x15, 0xf6, 0x0, 0x0, 0x9, 0xfe,
    0x81, 0x0, 0x2, 0x8e, 0xe2, 0x0, 0x0, 0x1e,
    0x98, 0x80, 0x0, 0xc9, 0x5f, 0x95, 0x7f, 0x50,
    0x5d, 0xfd, 0x60,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xd3, 0x46, 0xf5, 0x43, 0x0,
    0x2f, 0x20, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x2f,
    0x20, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x2f, 0x20,
    0x0, 0x2, 0xf2, 0x0, 0x0, 0x2f, 0x20, 0x0,
    0x2, 0xf2, 0x0,

    /* U+0055 "U" */
    0x1f, 0x30, 0x0, 0x5f, 0x1f, 0x30, 0x0, 0x5f,
    0x1f, 0x30, 0x0, 0x5f, 0x1f, 0x30, 0x0, 0x5f,
    0x1f, 0x30, 0x0, 0x5f, 0x1f, 0x30, 0x0, 0x5f,
    0xf, 0x40, 0x0, 0x6e, 0xd, 0x90, 0x0, 0xbb,
    0x6, 0xf8, 0x59, 0xf4, 0x0, 0x6d, 0xfd, 0x50,

    /* U+0056 "V" */
    0xcb, 0x0, 0x0, 0x7d, 0x6, 0xf0, 0x0, 0xc,
    0x80, 0x1f, 0x50, 0x1, 0xf3, 0x0, 0xba, 0x0,
    0x6d, 0x0, 0x5, 0xf0, 0xb, 0x80, 0x0, 0xf,
    0x50, 0xf3, 0x0, 0x0, 0xaa, 0x5d, 0x0, 0x0,
    0x4, 0xfb, 0x80, 0x0, 0x0, 0xe, 0xf3, 0x0,
    0x0, 0x0, 0x9d, 0x0, 0x0,

    /* U+0057 "W" */
    0xc9, 0x0, 0xb, 0x90, 0x0, 0xb8, 0x7d, 0x0,
    0xf, 0xd0, 0x0, 0xf4, 0x3f, 0x10, 0x3f, 0xf2,
    0x3, 0xf0, 0xe, 0x60, 0x7a, 0xd6, 0x7, 0xb0,
    0xa, 0xa0, 0xb6, 0x9a, 0xb, 0x70, 0x5, 0xe0,
    0xf2, 0x4f, 0xf, 0x20, 0x1, 0xf7, 0xd0, 0xf,
    0x7e, 0x0, 0x0, 0xce, 0x90, 0xb, 0xea, 0x0,
    0x0, 0x8f, 0x50, 0x7, 0xf5, 0x0, 0x0, 0x3f,
    0x10, 0x3, 0xf1, 0x0,

    /* U+0058 "X" */
    0x7f, 0x20, 0x0, 0xd9, 0xd, 0xb0, 0x8, 0xe0,
    0x3, 0xf5, 0x2f, 0x50, 0x0, 0x9e, 0xcb, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0x1f, 0xf2, 0x0,
    0x0, 0xac, 0xbc, 0x0, 0x4, 0xf2, 0x2f, 0x60,
    0xe, 0x80, 0x8, 0xf1, 0x8e, 0x0, 0x0, 0xda,

    /* U+0059 "Y" */
    0xad, 0x0, 0x2, 0xf4, 0x2f, 0x60, 0x9, 0xc0,
    0x9, 0xe0, 0x2f, 0x40, 0x1, 0xf8, 0xac, 0x0,
    0x0, 0x7f, 0xf3, 0x0, 0x0, 0xe, 0xb0, 0x0,
    0x0, 0xc, 0x80, 0x0, 0x0, 0xc, 0x80, 0x0,
    0x0, 0xc, 0x80, 0x0, 0x0, 0xc, 0x80, 0x0,

    /* U+005A "Z" */
    0x5f, 0xff, 0xff, 0x81, 0x44, 0x49, 0xf2, 0x0,
    0x0, 0xe8, 0x0, 0x0, 0x7e, 0x10, 0x0, 0x1f,
    0x60, 0x0, 0x9, 0xd0, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0xbb, 0x0, 0x0, 0x4f, 0x74, 0x44, 0x3b,
    0xff, 0xff, 0xfb,

    /* U+005B "[" */
    0x0, 0x0, 0xff, 0xf0, 0xf6, 0x30, 0xf3, 0x0,
    0xf3, 0x0, 0xf3, 0x0, 0xf3, 0x0, 0xf3, 0x0,
    0xf3, 0x0, 0xf3, 0x0, 0xf3, 0x0, 0xf4, 0x0,
    0xff, 0xf0, 0x33, 0x30,

    /* U+005C "\\" */
    0xa8, 0x0, 0x5, 0xd0, 0x0, 0xf, 0x30, 0x0,
    0xa8, 0x0, 0x5, 0xd0, 0x0, 0xf, 0x20, 0x0,
    0xb8, 0x0, 0x5, 0xd0, 0x0, 0x1f, 0x20, 0x0,
    0xb8,

    /* U+005D "]" */
    0x0, 0x0, 0xcf, 0xf4, 0x23, 0xf4, 0x0, 0xf4,
    0x0, 0xf4, 0x0, 0xf4, 0x0, 0xf4, 0x0, 0xf4,
    0x0, 0xf4, 0x0, 0xf4, 0x0, 0xf4, 0x0, 0xf4,
    0xcf, 0xf4, 0x23, 0x30,

    /* U+005E "^" */
    0x0, 0xdb, 0x0, 0x3, 0xff, 0x10, 0xa, 0x9a,
    0x80, 0x1f, 0x24, 0xe0, 0x7c, 0x0, 0xd5,

    /* U+005F "_" */
    0xff, 0xff, 0xf3, 0x22, 0x22, 0x20,

    /* U+0060 "`" */
    0xe, 0x40, 0x4, 0xc0,

    /* U+0061 "a" */
    0x4, 0xdf, 0xa1, 0xe, 0x73, 0xd9, 0x1, 0x0,
    0x7c, 0x6, 0xcd, 0xdd, 0x4f, 0x42, 0x8d, 0x5e,
    0x10, 0xbd, 0x9, 0xed, 0x9d,

    /* U+0062 "b" */
    0x2f, 0x10, 0x0, 0x2, 0xf1, 0x0, 0x0, 0x2f,
    0x10, 0x0, 0x2, 0xf6, 0xee, 0x70, 0x2f, 0xd5,
    0x7f, 0x52, 0xf4, 0x0, 0x9b, 0x2f, 0x20, 0x7,
    0xd2, 0xf4, 0x0, 0x9b, 0x2f, 0xd4, 0x6f, 0x52,
    0xf5, 0xee, 0x80,

    /* U+0063 "c" */
    0x2, 0xcf, 0xc2, 0xe, 0xa4, 0xa8, 0x5f, 0x0,
    0x0, 0x7d, 0x0, 0x0, 0x5f, 0x0, 0x0, 0xe,
    0xa4, 0xa8, 0x3, 0xcf, 0xc2,

    /* U+0064 "d" */
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0xb, 0x70, 0x3d, 0xf9, 0xb7, 0x1e, 0xa4,
    0xaf, 0x75, 0xf0, 0x0, 0xe7, 0x7d, 0x0, 0xc,
    0x75, 0xe0, 0x0, 0xe7, 0x1e, 0x81, 0x8f, 0x70,
    0x3d, 0xfa, 0xa7,

    /* U+0065 "e" */
    0x3, 0xcf, 0xc2, 0x0, 0xea, 0x3b, 0xc0, 0x5f,
    0x0, 0x2f, 0x27, 0xfe, 0xee, 0xf3, 0x5e, 0x11,
    0x11, 0x0, 0xe9, 0x37, 0xa0, 0x3, 0xcf, 0xd4,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x1c, 0xf7, 0x9, 0xc4, 0x20,
    0xb8, 0x0, 0xdf, 0xff, 0x22, 0xc9, 0x20, 0xb,
    0x80, 0x0, 0xb8, 0x0, 0xb, 0x80, 0x0, 0xb8,
    0x0, 0xb, 0x80, 0x0,

    /* U+0067 "g" */
    0x3, 0xdf, 0x9a, 0x71, 0xea, 0x4a, 0xf7, 0x5f,
    0x0, 0xe, 0x77, 0xd0, 0x0, 0xc7, 0x5e, 0x0,
    0xe, 0x71, 0xea, 0x49, 0xf7, 0x3, 0xdf, 0x9c,
    0x70, 0x10, 0x0, 0xe5, 0x1f, 0x74, 0x9f, 0x10,
    0x5d, 0xfc, 0x30,

    /* U+0068 "h" */
    0x2f, 0x10, 0x0, 0x2, 0xf1, 0x0, 0x0, 0x2f,
    0x10, 0x0, 0x2, 0xf7, 0xee, 0x50, 0x2f, 0xd5,
    0x9f, 0x12, 0xf3, 0x0, 0xf3, 0x2f, 0x10, 0xf,
    0x42, 0xf1, 0x0, 0xf4, 0x2f, 0x10, 0xf, 0x42,
    0xf1, 0x0, 0xf4,

    /* U+0069 "i" */
    0x2e, 0x21, 0xa1, 0x0, 0x2, 0xf1, 0x2f, 0x12,
    0xf1, 0x2f, 0x12, 0xf1, 0x2f, 0x12, 0xf1,

    /* U+006A "j" */
    0x0, 0x2e, 0x10, 0x1, 0xa0, 0x0, 0x0, 0x0,
    0x2, 0xf1, 0x0, 0x2f, 0x10, 0x2, 0xf1, 0x0,
    0x2f, 0x10, 0x2, 0xf1, 0x0, 0x2f, 0x10, 0x2,
    0xf1, 0x0, 0x2f, 0x10, 0x59, 0xe0, 0x4e, 0xe4,
    0x0,

    /* U+006B "k" */
    0x2f, 0x10, 0x0, 0x2, 0xf1, 0x0, 0x0, 0x2f,
    0x10, 0x0, 0x2, 0xf1, 0x9, 0xd0, 0x2f, 0x15,
    0xe2, 0x2, 0xf4, 0xf4, 0x0, 0x2f, 0xef, 0x50,
    0x2, 0xfa, 0x8e, 0x10, 0x2f, 0x20, 0xd9, 0x2,
    0xf1, 0x4, 0xf3,

    /* U+006C "l" */
    0x2f, 0x12, 0xf1, 0x2f, 0x12, 0xf1, 0x2f, 0x12,
    0xf1, 0x2f, 0x12, 0xf1, 0x2f, 0x12, 0xf1,

    /* U+006D "m" */
    0x2f, 0x7f, 0xd3, 0xaf, 0xa0, 0x2f, 0xa2, 0xcf,
    0x63, 0xf5, 0x2f, 0x20, 0x7e, 0x0, 0xb8, 0x2f,
    0x10, 0x6d, 0x0, 0xa8, 0x2f, 0x10, 0x6d, 0x0,
    0xa8, 0x2f, 0x10, 0x6d, 0x0, 0xa8, 0x2f, 0x10,
    0x6d, 0x0, 0xa8,

    /* U+006E "n" */
    0x2f, 0x6e, 0xe5, 0x2, 0xfb, 0x27, 0xf1, 0x2f,
    0x30, 0xf, 0x32, 0xf1, 0x0, 0xf4, 0x2f, 0x10,
    0xf, 0x42, 0xf1, 0x0, 0xf4, 0x2f, 0x10, 0xf,
    0x40,

    /* U+006F "o" */
    0x2, 0xbf, 0xc3, 0x0, 0xeb, 0x49, 0xf1, 0x5f,
    0x0, 0xe, 0x77, 0xd0, 0x0, 0xb9, 0x5f, 0x0,
    0xd, 0x70, 0xea, 0x49, 0xf1, 0x2, 0xcf, 0xc3,
    0x0,

    /* U+0070 "p" */
    0x2f, 0x6e, 0xe7, 0x2, 0xfc, 0x24, 0xf5, 0x2f,
    0x40, 0x9, 0xb2, 0xf2, 0x0, 0x7d, 0x2f, 0x40,
    0x9, 0xb2, 0xfd, 0x56, 0xf5, 0x2f, 0x6e, 0xe8,
    0x2, 0xf1, 0x0, 0x0, 0x2f, 0x10, 0x0, 0x2,
    0xf1, 0x0, 0x0,

    /* U+0071 "q" */
    0x3, 0xdf, 0x9a, 0x71, 0xea, 0x4a, 0xf7, 0x5f,
    0x0, 0xe, 0x77, 0xd0, 0x0, 0xc7, 0x5f, 0x0,
    0xe, 0x71, 0xea, 0x4a, 0xf7, 0x3, 0xdf, 0x9c,
    0x70, 0x0, 0x0, 0xb7, 0x0, 0x0, 0xb, 0x70,
    0x0, 0x0, 0xb7,

    /* U+0072 "r" */
    0x2f, 0x7f, 0x82, 0xfb, 0x21, 0x2f, 0x30, 0x2,
    0xf1, 0x0, 0x2f, 0x10, 0x2, 0xf1, 0x0, 0x2f,
    0x10, 0x0,

    /* U+0073 "s" */
    0x8, 0xee, 0x60, 0x5e, 0x46, 0xd0, 0x6f, 0x40,
    0x0, 0x8, 0xee, 0x60, 0x0, 0x6, 0xf1, 0x8b,
    0x36, 0xf1, 0x1b, 0xfe, 0x60,

    /* U+0074 "t" */
    0xd, 0x60, 0x0, 0xd6, 0x0, 0xef, 0xff, 0x32,
    0xd7, 0x20, 0xd, 0x60, 0x0, 0xd6, 0x0, 0xd,
    0x60, 0x0, 0xbb, 0x41, 0x3, 0xdf, 0x50,

    /* U+0075 "u" */
    0x3f, 0x0, 0x1f, 0x33, 0xf0, 0x1, 0xf3, 0x3f,
    0x0, 0x1f, 0x33, 0xf0, 0x1, 0xf3, 0x2f, 0x10,
    0x2f, 0x30, 0xf8, 0x2b, 0xf3, 0x5, 0xee, 0x7f,
    0x30,

    /* U+0076 "v" */
    0xba, 0x0, 0x2f, 0x15, 0xf0, 0x7, 0xb0, 0xe,
    0x50, 0xd5, 0x0, 0x9b, 0x2f, 0x0, 0x3, 0xf8,
    0xa0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0x7e, 0x0,
    0x0,

    /* U+0077 "w" */
    0xb8, 0x0, 0xd7, 0x0, 0xe4, 0x6d, 0x1, 0xfc,
    0x2, 0xe0, 0x1f, 0x26, 0xcf, 0x17, 0xa0, 0xc,
    0x7a, 0x5c, 0x5c, 0x40, 0x6, 0xce, 0x17, 0xbf,
    0x0, 0x1, 0xfb, 0x2, 0xfa, 0x0, 0x0, 0xc6,
    0x0, 0xc5, 0x0,

    /* U+0078 "x" */
    0x9d, 0x0, 0xab, 0x0, 0xe8, 0x5f, 0x10, 0x4,
    0xfe, 0x60, 0x0, 0xc, 0xe0, 0x0, 0x4, 0xfe,
    0x60, 0x0, 0xd7, 0x6f, 0x10, 0x9d, 0x0, 0xbb,
    0x0,

    /* U+0079 "y" */
    0xca, 0x0, 0x1f, 0x25, 0xf0, 0x7, 0xc0, 0xf,
    0x50, 0xc6, 0x0, 0x9b, 0x1f, 0x10, 0x3, 0xf8,
    0xb0, 0x0, 0xd, 0xf5, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x9, 0x90, 0x0, 0x45, 0xf3, 0x0, 0xb,
    0xf8, 0x0, 0x0,

    /* U+007A "z" */
    0x6f, 0xff, 0xf2, 0x1, 0x1b, 0xb0, 0x0, 0x4f,
    0x20, 0x0, 0xe7, 0x0, 0x8, 0xd0, 0x0, 0x2f,
    0x62, 0x20, 0x9f, 0xff, 0xf5,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x8, 0xf5, 0x2, 0xf7, 0x10,
    0x3f, 0x0, 0x4, 0xf0, 0x0, 0x4f, 0x0, 0x1a,
    0xd0, 0x9, 0xf4, 0x0, 0x3b, 0xc0, 0x0, 0x4f,
    0x0, 0x4, 0xf0, 0x0, 0x3f, 0x30, 0x0, 0xcf,
    0x50, 0x0, 0x21,

    /* U+007C "|" */
    0x12, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c,
    0x6c, 0x6c, 0x6c, 0x6c, 0x6c,

    /* U+007D "}" */
    0x0, 0x0, 0xb, 0xd3, 0x0, 0x2c, 0xb0, 0x0,
    0x7d, 0x0, 0x6, 0xd0, 0x0, 0x6e, 0x0, 0x3,
    0xf5, 0x0, 0xb, 0xf2, 0x3, 0xf7, 0x0, 0x6e,
    0x0, 0x6, 0xd0, 0x0, 0x9c, 0x0, 0xcf, 0x60,
    0x2, 0x10, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x91, 0xe3, 0x3e,
    0x1b, 0xfc, 0x1, 0x20, 0x2, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x89, 0x68, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0x1b, 0x20, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x6c, 0x0, 0x0,
    0x2, 0x22, 0x22, 0x5d, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x5f, 0x0, 0x0, 0x0, 0x1f, 0x10, 0xf,
    0x10, 0x0, 0x0, 0x1f, 0x10, 0xd, 0x50, 0x50,
    0x0, 0x2f, 0x68, 0x59, 0x90, 0xf0, 0x7f, 0xfe,
    0xc9, 0x42, 0xf8, 0xd0, 0x23, 0x0, 0x0, 0x0,
    0x7f, 0x60,

    /* U+64E6 "擦" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x3e, 0x0, 0x0, 0x0, 0xf0, 0xce, 0xee,
    0xee, 0xf1, 0x2e, 0xfd, 0xc8, 0x32, 0x60, 0xb1,
    0x4, 0xf3, 0x4e, 0xda, 0xec, 0xd0, 0x0, 0xf1,
    0xda, 0xc3, 0xaa, 0x60, 0x17, 0xfa, 0x7d, 0xb6,
    0x9e, 0x10, 0x49, 0xf2, 0xc7, 0x46, 0x64, 0xd3,
    0x0, 0xf1, 0x6e, 0xee, 0xee, 0x91, 0x0, 0xf0,
    0x8, 0x2e, 0x25, 0x0, 0x2, 0xe0, 0x9c, 0xe,
    0x3c, 0x90, 0x2f, 0xa1, 0xa0, 0xed, 0x0, 0x93,

    /* U+6A21 "模" */
    0x0, 0xe1, 0x5, 0xb0, 0x97, 0x0, 0x0, 0xe1,
    0xbe, 0xfd, 0xee, 0xd2, 0x5f, 0xff, 0x55, 0x92,
    0x86, 0x10, 0x3, 0xf2, 0x5d, 0x99, 0x9b, 0x80,
    0x8, 0xfa, 0x5e, 0xbb, 0xbd, 0x80, 0x1e, 0xfb,
    0x6b, 0x44, 0x48, 0x80, 0x89, 0xe1, 0x28, 0x8f,
    0x98, 0x40, 0x51, 0xe1, 0xcc, 0xcf, 0xcc, 0xc1,
    0x0, 0xe1, 0x22, 0xae, 0xc2, 0x20, 0x0, 0xe1,
    0x3b, 0xd1, 0xbc, 0x40, 0x0, 0xe1, 0xd7, 0x0,
    0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9664 "除" */
    0xff, 0xf4, 0x1, 0xda, 0x0, 0xf, 0x1e, 0x20,
    0xc4, 0x97, 0x0, 0xf1, 0xd2, 0xd8, 0x0, 0xc9,
    0xf, 0x59, 0xed, 0xcc, 0xcd, 0xd7, 0xf7, 0x81,
    0x14, 0xb8, 0x30, 0xf, 0x1e, 0x1, 0x1b, 0x71,
    0x10, 0xf0, 0xd9, 0xff, 0xff, 0xff, 0xf, 0xd,
    0x36, 0x2a, 0x69, 0x0, 0xfc, 0xd3, 0xe1, 0xa6,
    0x99, 0xf, 0x1, 0xd6, 0xb, 0x51, 0xe3, 0xf0,
    0x6, 0xf, 0xf2, 0x3, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xd0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F043 "" */
    0x0, 0x2, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0x2f, 0xf7, 0x0, 0x0, 0xa, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xfc, 0xe,
    0xff, 0xff, 0xff, 0xf2, 0xf9, 0xcf, 0xff, 0xff,
    0x3d, 0xc5, 0xff, 0xff, 0xf1, 0x6f, 0xa3, 0xbf,
    0xfa, 0x0, 0x8f, 0xff, 0xfb, 0x0, 0x0, 0x26,
    0x74, 0x0, 0x0,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x26, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfd,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x99,
    0x99, 0x99, 0x99, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x12, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9a, 0xaa, 0xaa, 0xaa,
    0xaa, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0xc3, 0xbf, 0xff, 0xff, 0xfb, 0x3c,
    0xff, 0x57, 0xff, 0xff, 0x75, 0xff, 0xff, 0xf9,
    0x3d, 0xd3, 0x9f, 0xff, 0xff, 0xff, 0xd5, 0x5d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x1, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x1, 0xef, 0xfa, 0x5d,
    0x10, 0x0, 0x1, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x1, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x1, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 52, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 47, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15, .adv_w = 72, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 23, .adv_w = 127, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 63, .adv_w = 111, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 112, .adv_w = 153, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 162, .adv_w = 138, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 212, .adv_w = 39, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 216, .adv_w = 69, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 249, .adv_w = 69, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 275, .adv_w = 84, .box_w = 5, .box_h = 6, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 290, .adv_w = 111, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 315, .adv_w = 49, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 323, .adv_w = 94, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 329, .adv_w = 46, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 332, .adv_w = 77, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 357, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 392, .adv_w = 111, .box_w = 4, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 412, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 447, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 482, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 517, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 552, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 587, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 622, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 657, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 692, .adv_w = 51, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 703, .adv_w = 53, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 718, .adv_w = 111, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 743, .adv_w = 111, .box_w = 7, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 761, .adv_w = 111, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 786, .adv_w = 86, .box_w = 7, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 821, .adv_w = 188, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 899, .adv_w = 131, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 944, .adv_w = 127, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 979, .adv_w = 127, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1019, .adv_w = 139, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1059, .adv_w = 115, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1089, .adv_w = 109, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1119, .adv_w = 136, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1159, .adv_w = 145, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1199, .adv_w = 53, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1209, .adv_w = 92, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1234, .adv_w = 134, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1274, .adv_w = 110, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1304, .adv_w = 167, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1349, .adv_w = 143, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1384, .adv_w = 148, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1429, .adv_w = 117, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1464, .adv_w = 148, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1524, .adv_w = 126, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1559, .adv_w = 111, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1594, .adv_w = 112, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1629, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1669, .adv_w = 130, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1714, .adv_w = 188, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1774, .adv_w = 130, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1814, .adv_w = 122, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1854, .adv_w = 112, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1889, .adv_w = 68, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1917, .adv_w = 77, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1942, .adv_w = 68, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1970, .adv_w = 94, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1985, .adv_w = 84, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1991, .adv_w = 61, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 1995, .adv_w = 106, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2016, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2051, .adv_w = 95, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2072, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2107, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2132, .adv_w = 69, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2160, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2195, .adv_w = 113, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2230, .adv_w = 48, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2245, .adv_w = 48, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2278, .adv_w = 104, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2313, .adv_w = 48, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2328, .adv_w = 165, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2363, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2388, .adv_w = 114, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2413, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2448, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2483, .adv_w = 75, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2501, .adv_w = 88, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2522, .adv_w = 74, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2545, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2570, .adv_w = 102, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2595, .adv_w = 152, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2630, .adv_w = 98, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2655, .adv_w = 103, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2690, .adv_w = 92, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2711, .adv_w = 74, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2746, .adv_w = 38, .box_w = 2, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2759, .adv_w = 74, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2794, .adv_w = 111, .box_w = 7, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 2808, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2874, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2946, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3018, .adv_w = 192, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3084, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3162, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3216, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3282, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3336, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3377, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3455, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3533, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3610, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3688, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3751, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3829, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3859, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3904, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3995, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4049, .adv_w = 132, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4108, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4156, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4228, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4289, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4350, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4398, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4464, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4503, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4542, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4603, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4620, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4683, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4787, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4885, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4951, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4990, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5029, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5109, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5163, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5241, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5326, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5387, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5459, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5520, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5581, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5635, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5694, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5766, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5838, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5901, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5992, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6051, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6141, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6209, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6277, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6345, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6413, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6481, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6569, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6634, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6706, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6791, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6859, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6918, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x5d7, 0xb12, 0x3755, 0x90f2, 0x90f9, 0x90fc, 0x90fd,
    0x90fe, 0x9102, 0x9104, 0x9106, 0x910a, 0x910d, 0x9112, 0x9117,
    0x9118, 0x9119, 0x912f, 0x9134, 0x9139, 0x913c, 0x913d, 0x913e,
    0x9142, 0x9143, 0x9144, 0x9145, 0x9158, 0x9159, 0x915f, 0x9161,
    0x9162, 0x9165, 0x9168, 0x9169, 0x916a, 0x916c, 0x9184, 0x9186,
    0x91b5, 0x91b6, 0x91b8, 0x91ba, 0x91d1, 0x91d8, 0x91db, 0x91e4,
    0x920d, 0x9215, 0x924c, 0x92dc, 0x9331, 0x9332, 0x9333, 0x9334,
    0x9335, 0x9378, 0x9384, 0x93de, 0x93f5, 0x964b, 0x98b3, 0x9993
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 24335, .range_length = 39316, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 64, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 4, 4,
    2, 0, 3, 0, 0, 13, 0, 0,
    3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 3, 4, -8,
    -27, -18, 4, -7, 0, -23, -2, 4,
    0, 0, 0, 0, 0, 0, -15, 0,
    -14, -5, 0, -9, -11, -1, -9, -9,
    -11, -9, -10, 0, 0, 0, -6, -19,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, -8, -7,
    0, 0, 0, -7, 0, -6, 0, -7,
    -4, -7, -11, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, -22, 0, -12, 5, 0, -13,
    -7, 0, 0, 0, -16, -3, -18, -13,
    0, -21, 4, 0, 0, -2, 0, 0,
    0, 0, 0, 0, -8, 0, -8, 0,
    0, -2, 0, 0, 0, -3, 0, 0,
    0, 3, 0, -7, 0, -9, -3, 0,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, -6, 4, 0, 6, -3, 0,
    0, 0, 1, 0, -1, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -4, 0, 0, 0,
    0, 0, 2, 0, 2, -2, 0, 2,
    0, 0, 0, -2, 0, 0, -2, 0,
    -2, 0, -2, -3, 0, 0, -2, -2,
    -2, -4, -2, -4, 0, -2, 5, 0,
    1, -25, -11, 8, -1, 0, -27, 0,
    4, 0, 0, 0, 0, 0, 0, -8,
    0, -5, -2, 0, -3, 0, -2, 0,
    -4, -7, -4, -5, 0, 0, 0, 0,
    3, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -6, -3,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, 0, -21, 3, 0, -1,
    -11, -3, 0, -3, 0, -5, 0, 0,
    0, 0, 0, -6, 0, -7, -8, 0,
    -3, -3, -8, -8, -13, -7, -13, 0,
    -10, -20, 0, -17, 5, 0, -14, -9,
    0, 3, -2, -25, -8, -28, -21, 0,
    -34, 0, -1, 0, -4, -4, 0, 0,
    0, -5, -5, -18, 0, -18, 0, -2,
    2, 0, 2, -28, -16, 3, 0, 0,
    -31, 0, 0, 0, -1, -1, -5, 0,
    -6, -6, 0, -6, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, -2,
    0, -2, 7, 0, -1, -2, 0, 0,
    1, -2, -2, -5, -3, 0, -9, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 4,
    0, 0, -3, 0, 0, -4, 1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 6, 0, -15,
    -22, -16, 7, -6, 0, -27, 0, 4,
    0, 4, 4, 0, 0, 0, -23, 0,
    -21, -9, 0, -18, -21, -6, -17, -20,
    -21, -20, -17, -2, 3, 0, -5, -15,
    -13, 0, -4, 0, -14, 0, 4, 0,
    0, 0, 0, 0, 0, -15, 0, -12,
    -3, 0, -8, -9, 0, -7, -4, -6,
    -4, -7, 0, 0, 4, -18, 2, 0,
    2, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, -7, 0,
    0, -3, -3, -6, -6, -12, 0, -12,
    0, -6, 3, 4, -14, -26, -21, 2,
    -11, 0, -26, -5, 0, 0, 0, 0,
    0, 0, 0, -22, 0, -21, -10, 0,
    -16, -18, -7, -15, -14, -13, -14, -15,
    0, 0, 2, -9, 4, 0, 2, -5,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, -6, 0, -6, 0, 0,
    -8, 0, 0, 0, 0, -6, 0, 0,
    0, 0, -17, 0, -14, -13, -2, -19,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -2, 0, 0, -10,
    0, -2, -6, 0, -8, 0, 0, 0,
    0, -21, 0, -14, -12, -7, -21, 0,
    -2, 0, 0, -2, 0, 0, 0, -1,
    0, -4, -5, -4, -4, 0, 1, 0,
    3, 5, 0, -2, 0, 0, 0, 0,
    -15, 0, -10, -7, 3, -15, 0, 0,
    0, -1, 2, 0, 0, 0, 4, 0,
    0, 1, 0, 3, 0, 0, 3, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 3, -1,
    0, -5, 0, 0, 0, 0, -14, 0,
    -12, -9, -3, -18, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, -1,
    0, 0, 0, 9, 0, -1, -15, 0,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, -5, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, -18, 0, -9, -8,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 8, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, -5, 5, 0, -9, 0, 0,
    0, 0, -18, 0, -12, -11, 0, -16,
    0, -6, 0, -4, 0, 0, 0, -2,
    0, -2, 0, 0, 0, 0, 0, 5,
    0, 2, -21, -9, -6, 0, 0, -23,
    0, 0, 0, -8, 0, -10, -14, 0,
    -8, 0, -6, 0, 0, 0, -1, 6,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    -19, 0, -14, -10, -1, -20, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    -2, 0, -2, 2, 0, -1, 2, 0,
    5, 0, -5, 0, 0, 0, 0, -13,
    0, -9, 0, 0, -13, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -17,
    -8, -5, 0, 0, -15, 0, -20, 0,
    -9, -4, -12, -14, 0, -4, 0, -4,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 2, 0,
    -8, 0, 0, 0, 0, -21, 0, -11,
    -6, 0, -13, 0, -3, 0, -5, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 0, -6,
    0, 0, 0, 0, -19, 0, -11, -6,
    0, -14, 0, -3, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_12 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 12,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_12*/

