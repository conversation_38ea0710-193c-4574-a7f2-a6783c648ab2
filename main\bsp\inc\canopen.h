#ifndef __CANOPEN_H__
#define __CANOPEN_H__

#include "esp_err.h"
#include "driver/twai.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// CANopen ID定义
#define RPDO1_BASE_ID   0x200   // RPDO1基础ID
#define RPDO2_BASE_ID   0x300   // RPDO2基础ID  
#define RPDO3_BASE_ID   0x400   // RPDO3基础ID
#define TPDO1_BASE_ID   0x180   // TPDO1基础ID
#define TPDO2_BASE_ID   0x280   // TPDO2基础ID
#define SDO_REQ_BASE_ID 0x600   // SDO请求基础ID
#define SDO_RSP_BASE_ID 0x580   // SDO响应基础ID

// 默认参数
#define DEFAULT_NODE_ID     1       // 默认节点ID
#define DEFAULT_TPDO_PERIOD 50      // 默认TPDO发送周期(ms)

// CANopen配置结构体
typedef struct {
    uint8_t node_id;           // 节点ID
    uint16_t tpdo_period;      // TPDO发送周期(ms)
    bool write_access;         // 是否允许写操作
    uint8_t error_register;    // 错误寄存器
} canopen_config_t;

// 对象字典访问结构体
typedef struct {
    uint16_t index;            // 索引
    uint8_t sub_index;         // 子索引
    uint8_t *od_pointer;       // 数据指针
} dict_object_t;

// 全局变量
extern canopen_config_t canopen_config;
extern uint8_t tpdo1_data[8];
extern uint8_t rpdo1_data[8];
extern int32_t target_position;
extern int32_t target_speed;
extern int32_t target_iq;
extern int32_t real_speed_filter;
extern int16_t pos_actual;

// 函数声明
esp_err_t canopen_init(uint8_t node_id, uint16_t tpdo_period);
esp_err_t canopen_start(void);
esp_err_t canopen_stop(void);

void canopen_process_received_tpdo(twai_message_t *message);
void canopen_tpdo_task(void *pvParameters);
void canopen_receive_task(void *pvParameters);
esp_err_t canopen_send_rpdo(uint16_t rpdo_type, uint8_t target_node_id, int32_t target_value);

#endif /* __CANOPEN_H__ */
